import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

interface Service {
  icon: React.ComponentType<{ className?: string; size?: number }>;
  title: string;
  description: string;
  features: string[];
  color: string;
  link?: string;
}

interface MobileServicesBentoProps {
  services: Service[];
}

export const MobileServicesBento: React.FC<MobileServicesBentoProps> = ({ services }) => {
  return (
    <div className="grid grid-cols-1 gap-6 lg:hidden">
      {services.map((service, index) => {
        const Icon = service.icon;
        const CardWrapper = service.link ? Link : 'div';
        const cardProps = service.link ? { to: service.link } : {};

        return (
          <CardWrapper key={index} {...cardProps} className={service.link ? "block" : ""}>
            <motion.div
              className="relative bg-gradient-to-br from-gray-900/90 to-black/95 backdrop-blur-sm border border-gray-800/50 rounded-2xl p-6 overflow-hidden group hover:border-gray-700/50 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -2 }}
            >
              {/* Background gradient overlay */}
              <div 
                className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}
              />
              
              {/* Subtle glow effect */}
              <div 
                className={`absolute -inset-1 bg-gradient-to-r ${service.color} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300`}
              />

              <div className="relative z-10">
                {/* Header with icon and title */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="text-white" size={24} />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-gray-100 transition-colors">
                        {service.title}
                      </h3>
                    </div>
                  </div>
                  {service.link && (
                    <ArrowRight className="text-gray-400 group-hover:text-white group-hover:translate-x-1 transition-all duration-300" size={20} />
                  )}
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <div className="space-y-2">
                  {service.features.slice(0, 3).map((feature, featureIndex) => (
                    <motion.div 
                      key={featureIndex}
                      className="flex items-center space-x-2"
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 + featureIndex * 0.05 }}
                      viewport={{ once: true }}
                    >
                      <div className={`w-1.5 h-1.5 bg-gradient-to-r ${service.color} rounded-full`} />
                      <span className="text-gray-400 text-xs">{feature}</span>
                    </motion.div>
                  ))}
                  {service.features.length > 3 && (
                    <div className="flex items-center space-x-2 mt-2">
                      <div className="w-1.5 h-1.5 bg-gray-500 rounded-full" />
                      <span className="text-gray-500 text-xs">+{service.features.length - 3} more features</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </CardWrapper>
        );
      })}
    </div>
  );
};

// Alternative compact grid layout for mobile
export const MobileServicesBentoCompact: React.FC<MobileServicesBentoProps> = ({ services }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:hidden">
      {services.map((service, index) => {
        const Icon = service.icon;
        const CardWrapper = service.link ? Link : 'div';
        const cardProps = service.link ? { to: service.link } : {};

        return (
          <CardWrapper key={index} {...cardProps} className={service.link ? "block" : ""}>
            <motion.div
              className="relative bg-gradient-to-br from-gray-900/80 to-black/90 backdrop-blur-sm border border-gray-800/40 rounded-xl p-4 overflow-hidden group hover:border-gray-700/50 transition-all duration-300 min-h-[160px]"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: index * 0.08 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              {/* Background gradient */}
              <div 
                className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-5 group-hover:opacity-15 transition-opacity duration-300`}
              />

              <div className="relative z-10 h-full flex flex-col">
                {/* Icon and title */}
                <div className="flex items-center space-x-3 mb-3">
                  <div className={`w-10 h-10 bg-gradient-to-r ${service.color} rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="text-white" size={18} />
                  </div>
                  {service.link && (
                    <ArrowRight className="text-gray-400 group-hover:text-white ml-auto transition-colors duration-300" size={16} />
                  )}
                </div>

                <h3 className="text-base font-semibold text-white mb-2 leading-tight group-hover:text-gray-100 transition-colors">
                  {service.title}
                </h3>

                <p className="text-gray-400 text-xs mb-3 leading-relaxed flex-1">
                  {service.description.length > 80 
                    ? `${service.description.substring(0, 80)}...` 
                    : service.description
                  }
                </p>

                {/* Key features (max 2) */}
                <div className="space-y-1">
                  {service.features.slice(0, 2).map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center space-x-2">
                      <div className={`w-1 h-1 bg-gradient-to-r ${service.color} rounded-full`} />
                      <span className="text-gray-500 text-xs">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </CardWrapper>
        );
      })}
    </div>
  );
};
