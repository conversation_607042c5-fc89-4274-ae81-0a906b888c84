import React, { useEffect } from 'react'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  noIndex?: boolean
  canonical?: string
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Delta Xero Creations - Affordable Web & App Development Services',
  description = 'Delta Xero Creations provides affordable, high-quality web development, mobile app development, UI/UX design, and digital transformation services. Get your project started today!',
  keywords = 'web development, mobile app development, UI/UX design, digital transformation, affordable web services, custom software development, React development, Node.js, API development, brand strategy',
  image = 'https://deltaxerocreations.com/og-image.jpg',
  url = 'https://deltaxerocreations.com',
  type = 'website',
  author = 'Delta Xero Creations',
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noIndex = false,
  canonical
}) => {
  const siteTitle = 'Delta Xero Creations'
  const fullTitle = title.includes(siteTitle) ? title : `${title} | ${siteTitle}`
  const currentUrl = canonical || url

  useEffect(() => {
    // Update document title
    document.title = fullTitle

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`
      let meta = document.querySelector(selector) as HTMLMetaElement
      if (!meta) {
        meta = document.createElement('meta')
        if (property) {
          meta.setAttribute('property', name)
        } else {
          meta.setAttribute('name', name)
        }
        document.head.appendChild(meta)
      }
      meta.setAttribute('content', content)
    }

    // Basic meta tags
    updateMetaTag('description', description)
    updateMetaTag('keywords', keywords)
    updateMetaTag('author', author)
    updateMetaTag('robots', noIndex ? 'noindex, nofollow' : 'index, follow')

    // Open Graph tags
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:title', fullTitle, true)
    updateMetaTag('og:description', description, true)
    updateMetaTag('og:image', image, true)
    updateMetaTag('og:url', currentUrl, true)
    updateMetaTag('og:site_name', siteTitle, true)
    updateMetaTag('og:locale', 'en_US', true)

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image')
    updateMetaTag('twitter:title', fullTitle)
    updateMetaTag('twitter:description', description)
    updateMetaTag('twitter:image', image)
    updateMetaTag('twitter:site', '@deltaxerocreations')

    // Article specific tags
    if (type === 'article') {
      if (author) updateMetaTag('article:author', author, true)
      if (publishedTime) updateMetaTag('article:published_time', publishedTime, true)
      if (modifiedTime) updateMetaTag('article:modified_time', modifiedTime, true)
      if (section) updateMetaTag('article:section', section, true)
      tags.forEach((tag, index) => {
        updateMetaTag(`article:tag-${index}`, tag, true)
      })
    }

    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    if (!canonical) {
      canonical = document.createElement('link')
      canonical.setAttribute('rel', 'canonical')
      document.head.appendChild(canonical)
    }
    canonical.setAttribute('href', currentUrl)

    // Add structured data
    const addStructuredData = (data: object, id: string) => {
      let script = document.querySelector(`script[data-schema="${id}"]`) as HTMLScriptElement
      if (!script) {
        script = document.createElement('script')
        script.setAttribute('type', 'application/ld+json')
        script.setAttribute('data-schema', id)
        document.head.appendChild(script)
      }
      script.textContent = JSON.stringify(data)
    }

    // Organization schema
    addStructuredData({
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Delta Xero Creations",
      "description": "Affordable web and app development services",
      "url": "https://deltaxerocreations.com",
      "logo": "https://deltaxerocreations.com/logo.png",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://twitter.com/deltaxerocreations",
        "https://linkedin.com/company/deltaxerocreations",
        "https://github.com/deltaxerocreations"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
      },
      "foundingDate": "2024",
      "numberOfEmployees": "10-50",
      "industry": "Software Development",
      "services": [
        "Web Development",
        "Mobile App Development",
        "UI/UX Design",
        "API Development",
        "Digital Transformation",
        "Brand Strategy"
      ]
    }, 'organization')

    // Website schema
    addStructuredData({
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Delta Xero Creations",
      "url": "https://deltaxerocreations.com",
      "description": description,
      "publisher": {
        "@type": "Organization",
        "name": "Delta Xero Creations"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://deltaxerocreations.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }, 'website')

  }, [fullTitle, description, keywords, image, currentUrl, type, author, publishedTime, modifiedTime, section, tags, noIndex])

  return null
}

export default SEOHead
