import React, { useState, useEffect } from 'react';
import { GooeyText } from '@/components/ui/gooey-text-morphing';
import { motion, AnimatePresence } from 'framer-motion';

interface LoadingScreenProps {
  isLoading: boolean;
  onLoadingComplete: () => void;
  texts?: string[];
  morphTime?: number;
  cooldownTime?: number;
  minDisplayTime?: number; // Minimum time to show loading screen (in ms)
  imageProgress?: {
    loaded: number;
    total: number;
    failed: number;
  };
  overallProgress?: number;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  isLoading,
  onLoadingComplete,
  texts = ["Delta", "Xero", "Creations", "Welcome"],
  morphTime = 1.2,
  cooldownTime = 0.4,
  minDisplayTime = 6000, // Default 6 seconds for smoother experience
  imageProgress = { loaded: 0, total: 0, failed: 0 },
  overallProgress = 0
}) => {
  const [hasCompletedCycle, setHasCompletedCycle] = useState(false);
  const [cycleCount, setCycleCount] = useState(0);
  const startTime = React.useRef(Date.now());

  useEffect(() => {
    // Calculate time for one complete cycle with buffer
    const timePerText = (morphTime + cooldownTime) * 1000;
    const oneCycleTime = timePerText * texts.length;
    const actualMinTime = Math.max(minDisplayTime, oneCycleTime * 1.2); // 20% buffer

    // Track cycle completion
    const cycleTimer = setInterval(() => {
      const elapsed = Date.now() - startTime.current;
      const completedCycles = Math.floor(elapsed / oneCycleTime);
      setCycleCount(completedCycles);

      if (elapsed >= actualMinTime && completedCycles >= 1) {
        setHasCompletedCycle(true);
        clearInterval(cycleTimer);
      }
    }, 100);

    return () => clearInterval(cycleTimer);
  }, [texts.length, morphTime, cooldownTime, minDisplayTime]);

  useEffect(() => {
    // Only complete loading when both conditions are met:
    // 1. External loading is complete (!isLoading)
    // 2. At least one cycle has completed (hasCompletedCycle)
    if (!isLoading && hasCompletedCycle) {
      // Add a small delay for smooth transition
      const exitTimer = setTimeout(() => {
        onLoadingComplete();
      }, 500);

      return () => clearTimeout(exitTimer);
    }
  }, [isLoading, hasCompletedCycle, onLoadingComplete]);

  return (
    <AnimatePresence>
      {(isLoading || !hasCompletedCycle) && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ 
            opacity: 0,
            scale: 1.1,
            transition: { 
              duration: 0.8,
              ease: "easeInOut"
            }
          }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-dark-950"
        >
          {/* Background gradient animation */}
          <div className="absolute inset-0 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-primary-500/5 animate-pulse" />
          </div>

          {/* Main content */}
          <div className="relative z-10 flex flex-col items-center justify-center space-y-8">
            {/* Logo */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-8"
            >
              <img
                src="https://sdwgyjjcxdhdlcuvjadq.supabase.co/storage/v1/object/public/invoices//delta_zero_vertical_logo-removebg-preview.png"
                alt="Delta Xero Creations"
                className="h-20 w-auto"
              />
            </motion.div>

            {/* Gooey Text */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="h-32 flex items-center justify-center"
            >
              <GooeyText
                texts={texts}
                morphTime={morphTime}
                cooldownTime={cooldownTime}
                className="font-bold"
                textClassName="text-4xl md:text-6xl lg:text-7xl bg-gradient-to-r from-primary-400 via-white to-primary-400 bg-clip-text text-transparent"
              />
            </motion.div>

            {/* Loading indicator */}
            

            {/* Progress indicator */}
            <div className="w-full max-w-md space-y-4">
              {/* Overall progress bar */}
              <div className="relative">
                
                <div className="absolute inset-0 h-1 bg-gray-800/50 rounded-full -z-10" />
              </div>

              {/* Image loading progress */}
              {imageProgress.total > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center space-y-2"
                >
                  
                  <div className="relative">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{
                        width: `${(imageProgress.loaded / imageProgress.total) * 100}%`
                      }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="h-0.5 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"
                    />
                    <div className="absolute inset-0 h-0.5 bg-gray-800/30 rounded-full -z-10" />
                  </div>
                </motion.div>
              )}

              {/* Loading status text */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center text-xs text-gray-500"
              >
                {!hasCompletedCycle ? 'Initializing...' :
                 imageProgress.loaded < imageProgress.total ? 'Loading assets...' :
                 'Almost ready...'}
              </motion.div>
            </div>
          </div>

          {/* Subtle particle effect */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-primary-400/30 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-20, -100],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
