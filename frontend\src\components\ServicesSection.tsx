import { useRef, useEffect } from 'react'
import { Code, Smartphone, Palette, Zap } from 'lucide-react'
import { DesktopServicesBento } from './ui/desktop-services-bento'
import { MobileServicesBento } from './ui/mobile-services-bento'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { getSmoothScrollInstance } from '../hooks/useSmoothScroll'

gsap.registerPlugin(ScrollTrigger)

const ServicesSection = () => {
  const servicesRef = useRef(null)
  const backgroundTextRef = useRef(null)

  useEffect(() => {
    const initAnimations = () => {
      const lenis = getSmoothScrollInstance()
      if (!lenis) {
        setTimeout(initAnimations, 100)
        return
      }

      // Disable background text animation to prevent jitter
      // The background text will remain static for better performance
      if (backgroundTextRef.current) {
        gsap.set(backgroundTextRef.current, {
          opacity: 0.12,
          y: 0
        })
      }
    }

    initAnimations()

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
      if (backgroundTextRef.current) {
        gsap.set(backgroundTextRef.current, { willChange: 'auto' })
      }
    }
  }, [])

  const services = [
    {
      icon: Code,
      title: 'Digital Experience Platforms',
      description: 'Scale your business by creating value-driven solutions that align with your vision',
      color: 'from-blue-500 to-cyan-500',
      features: ['React/Next.js', 'Vue.js', 'Node.js Backend', 'Cloud Integration'],
      link: '/digital-experience-platforms'
    },
    {
      icon: Smartphone,
      title: 'Mobile Engineering',
      description: 'Native and cross-platform mobile applications that deliver exceptional user experiences',
      color: 'from-purple-500 to-pink-500',
      features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment'],
      link: '/mobile-engineering'
    },
    {
      icon: Palette,
      title: 'Experience Design',
      description: 'Beautiful, user-centered designs that convert visitors into customers',
      color: 'from-green-500 to-teal-500',
      features: ['User Research', 'Wireframing', 'Prototyping', 'Design Systems'],
      link: '/experience-design'
    },
    {
      icon: Zap,
      title: 'Performance Optimization',
      description: 'Lightning-fast websites and applications that scale with your business',
      color: 'from-yellow-500 to-orange-500',
      features: ['Code Optimization', 'Database Tuning', 'CDN Setup', 'Monitoring'],
      link: '/performance-optimization'
    }
  ]

  return (
    <section ref={servicesRef} className="py-44 bg-dark-950 relative overflow-hidden services-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Large SERVICES Text Background - Top positioned, half hidden */}
        <div
          ref={backgroundTextRef}
          className="absolute left-1/2 top-0 transform -translate-x-1/2 -translate-y-[65%] pointer-events-none select-none z-0 gpu-accelerated"
        >
          <div className="text-[3rem] sm:text-[4rem] md:text-[6rem] lg:text-[8rem] xl:text-[10rem] font-black whitespace-nowrap bg-gradient-to-br from-primary-400/15 via-primary-500/10 to-primary-600/20 bg-clip-text text-transparent">
            OUR SERVICES
          </div>
        </div>

        {/* Desktop Bento Grid */}
        <DesktopServicesBento services={services} className="relative z-20" />

        {/* Mobile Optimized Bento Grid */}
        <div className="relative z-20">
          <MobileServicesBento services={services} />
        </div>
      </div>
    </section>
  )
}

export default ServicesSection
