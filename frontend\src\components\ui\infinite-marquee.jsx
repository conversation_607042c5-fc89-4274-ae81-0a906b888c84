import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const InfiniteMarquee = ({ children, speed = 50, direction = 'left', className = '' }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Use CSS animation for mobile to reduce JavaScript overhead
  if (isMobile) {
    return (
      <div className={`overflow-hidden whitespace-nowrap ${className}`}>
        <div
          className="inline-block"
          style={{
            animation: `marquee-mobile ${speed}s linear infinite`,
            willChange: 'transform',
          }}
        >
          <div className="flex items-center">
            {children}
            {children}
          </div>
        </div>
      </div>
    );
  }

  // Use Framer Motion for desktop
  return (
    <div className={`overflow-hidden whitespace-nowrap ${className}`}>
      <motion.div
        className="inline-block"
        animate={{
          x: direction === 'left' ? ['0%', '-50%'] : ['-50%', '0%'],
        }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: 'loop',
            duration: speed,
            ease: 'linear',
          },
        }}
        style={{ willChange: 'transform' }}
      >
        <div className="flex items-center">
          {children}
          {children}
        </div>
      </motion.div>
    </div>
  );
};

export default InfiniteMarquee;
