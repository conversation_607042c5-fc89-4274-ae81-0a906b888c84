import React from 'react';
import { cn } from '@/lib/utils';

interface ImagePlaceholderProps {
  width?: number;
  height?: number;
  text?: string;
  className?: string;
  alt?: string;
}

export const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  width = 400,
  height = 300,
  text = 'Delta Xero Creations',
  className,
  alt = 'Placeholder image'
}) => {
  // Create a data URL for the placeholder
  const createPlaceholder = () => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return '';
    
    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#1a1a2e');
    gradient.addColorStop(0.5, '#16213e');
    gradient.addColorStop(1, '#0f172a');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // Logo/Brand text
    ctx.fillStyle = '#3b82f6';
    ctx.font = `bold ${Math.min(width, height) / 15}px Inter, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, width / 2, height / 2);
    
    // Subtitle
    ctx.fillStyle = '#64748b';
    ctx.font = `${Math.min(width, height) / 25}px Inter, sans-serif`;
    ctx.fillText('Professional Web Solutions', width / 2, height / 2 + 30);
    
    return canvas.toDataURL();
  };

  return (
    <div 
      className={cn(
        "bg-gradient-to-br from-dark-800 to-dark-900 flex flex-col items-center justify-center text-center border border-gray-700/50 rounded-lg overflow-hidden",
        className
      )}
      style={{ width, height }}
    >
      <div className="text-primary-400 font-bold text-lg mb-2">
        Delta Xero Creations
      </div>
      <div className="text-gray-400 text-sm">
        Professional Web Solutions
      </div>
      <div className="mt-4 w-16 h-16 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center">
        <span className="text-white font-bold text-xl">DX</span>
      </div>
    </div>
  );
};

// Hook to create placeholder images
export const usePlaceholderImage = (width = 400, height = 300, text = 'Delta Xero Creations') => {
  const [placeholderUrl, setPlaceholderUrl] = React.useState('');

  React.useEffect(() => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return;
    
    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#1a1a2e');
    gradient.addColorStop(0.5, '#16213e');
    gradient.addColorStop(1, '#0f172a');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // Logo/Brand text
    ctx.fillStyle = '#3b82f6';
    ctx.font = `bold ${Math.min(width, height) / 15}px Inter, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, width / 2, height / 2);
    
    // Subtitle
    ctx.fillStyle = '#64748b';
    ctx.font = `${Math.min(width, height) / 25}px Inter, sans-serif`;
    ctx.fillText('Professional Web Solutions', width / 2, height / 2 + 30);
    
    // Logo symbol
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(width / 2 - 20, height / 2 - 60, 40, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 20px Inter, sans-serif';
    ctx.fillText('DX', width / 2, height / 2 - 40);
    
    setPlaceholderUrl(canvas.toDataURL());
  }, [width, height, text]);

  return placeholderUrl;
};

export default ImagePlaceholder;
