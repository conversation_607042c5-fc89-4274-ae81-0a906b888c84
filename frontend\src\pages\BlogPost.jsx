import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useParams } from 'react-router-dom';
import { ArrowLeft, Calendar, Clock, User, Tag } from 'lucide-react';
import { getPostBySlug, getRelatedPosts } from '../data/blogPosts';

const BlogPost = () => {
  const { slug } = useParams();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Get the post by slug from our data
  const post = getPostBySlug(slug);

  // If post not found, show 404
  if (!post) {
    return (
      <div className="min-h-screen bg-dark-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Post Not Found</h1>
          <p className="text-gray-400 mb-8">The blog post you're looking for doesn't exist.</p>
          <Link
            to="/blog"
            className="bg-primary-500 text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  // Get related posts
  const relatedPosts = getRelatedPosts(post);



  return (
    <section className="py-32 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Breadcrumbs */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center text-sm text-gray-400">
              <Link to="/" className="hover:text-primary-400 transition-colors">Home</Link>
              <span className="mx-2">/</span>
              <Link to="/blog" className="hover:text-primary-400 transition-colors">Blog</Link>
              <span className="mx-2">/</span>
              <span className="text-gray-300">{post.title}</span>
            </div>
          </motion.div>

          {/* Post Header */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {post.title}
            </h1>

            <div className="flex items-center mb-6">
              <div className="relative w-12 h-12 mr-4">
                <img
                  src={post.author.avatar}
                  alt={post.author.name}
                  className="w-12 h-12 rounded-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextElementSibling.style.display = 'flex';
                  }}
                />
                <div className="absolute inset-0 hidden bg-gradient-to-r from-primary-500 to-blue-500 rounded-full items-center justify-center">
                  <span className="text-white font-bold text-xs">DX</span>
                </div>
              </div>
              <div>
                <div className="text-white font-medium">{post.author.name}</div>
                <div className="flex items-center text-sm text-gray-400">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime} min read</span>
                  <span className="mx-2">•</span>
                  <span className="text-primary-400">{post.category}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Featured Image */}
          <motion.div
            className="mb-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="relative w-full min-h-[300px] bg-gradient-to-br from-dark-800 to-dark-900 rounded-xl flex items-center justify-center">
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-auto rounded-xl"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-8">
                <div className="w-20 h-20 bg-gradient-to-r from-primary-500 to-blue-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-white font-bold text-2xl">DX</span>
                </div>
                <div className="text-primary-400 font-bold text-xl mb-2">Delta Xero Creations</div>
                <div className="text-gray-400 text-lg">{post.category}</div>
                <div className="text-gray-500 text-sm mt-4 max-w-md">
                  Professional web development and digital solutions
                </div>
              </div>
            </div>
          </motion.div>

          {/* Post Content */}
          <motion.div
            className="prose prose-invert prose-lg max-w-none mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Author Bio */}
          <motion.div
            className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex items-center">
              <img
                src={post.author.avatar}
                alt={post.author.name}
                className="w-16 h-16 rounded-full mr-4"
              />
              <div>
                <h3 className="text-xl font-bold text-white mb-2">About {post.author.name}</h3>
                <p className="text-gray-300">{post.author.bio}</p>
              </div>
            </div>
          </motion.div>

          {/* Related Posts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h2 className="text-2xl font-bold text-white mb-6">Related Articles</h2>
            {relatedPosts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {relatedPosts.map(relatedPost => (
                  <Link key={relatedPost.id} to={`/blog/${relatedPost.slug}`} className="block group">
                    <div className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden hover:shadow-lg hover:shadow-primary-500/10 hover:border-primary-500/30 transition-all duration-300">
                      <div className="relative h-40 bg-gradient-to-br from-dark-800 to-dark-900 flex items-center justify-center">
                        <img
                          src={relatedPost.image}
                          alt={relatedPost.title}
                          className="w-full h-40 object-cover transition-transform duration-500 group-hover:scale-105"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextElementSibling.style.display = 'flex';
                          }}
                        />
                        <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center mb-2">
                            <span className="text-white font-bold text-xs">DX</span>
                          </div>
                          <div className="text-primary-400 font-semibold text-xs">Delta Xero</div>
                        </div>
                        <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
                          {relatedPost.category}
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="text-lg font-bold text-white group-hover:text-primary-400 transition-colors">
                          {relatedPost.title}
                        </h3>
                        <p className="text-gray-400 text-sm mt-2 line-clamp-2">
                          {relatedPost.excerpt}
                        </p>
                        <div className="flex items-center mt-3 text-xs text-gray-500">
                          <Calendar size={12} className="mr-1" />
                          <span>{relatedPost.date}</span>
                          <Clock size={12} className="ml-3 mr-1" />
                          <span>{relatedPost.readTime} min read</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-400">No related articles found.</p>
                <Link
                  to="/blog"
                  className="text-primary-400 hover:text-primary-300 transition-colors mt-2 inline-block"
                >
                  Browse all articles →
                </Link>
              </div>
            )}
          </motion.div>

          {/* Back to Blog */}
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Link
              to="/blog"
              className="inline-flex items-center text-primary-400 hover:text-primary-300 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to All Articles
            </Link>
          </motion.div>

          {/* Newsletter CTA */}
          <motion.div
            className="mt-16 text-center bg-dark-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <h3 className="text-2xl font-bold text-white mb-4">
              Stay Updated with <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Delta Xero</span>
            </h3>
            <p className="text-gray-300 mb-6">
              Get the latest insights on web development, design trends, and business growth delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary-500 focus:outline-none"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
              >
                Subscribe
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BlogPost;
