import { useState, useEffect } from 'react';
import { ALL_IMAGES, preloadImages } from '../utils/imagePreloader';

interface UseAppLoadingOptions {
  simulateDbLoad?: boolean;
  preloadImages?: boolean;
  minLoadTime?: number;
  maxLoadTime?: number;
  imagesToPreload?: string[];
}

// Default images to preload - add your critical images here
const DEFAULT_IMAGES_TO_PRELOAD = [
  // Logo and branding
  'https://ik.imagekit.io/buildwithangga/logo-deltaxero.png',

  // Hero/background images
  '/og-home.jpg',
  '/og-about.jpg',
  '/og-services.jpg',

  // Portfolio/project images (add your actual image URLs)
  'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',

  // Company logos for marquee
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg',
  'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg'
];

export const useAppLoading = ({
  simulateDbLoad = true,
  preloadImages = true,
  minLoadTime = 1000,
  maxLoadTime = 3000,
  imagesToPreload = ALL_IMAGES.map(img => img.src)
}: UseAppLoadingOptions = {}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingSteps, setLoadingSteps] = useState({
    database: simulateDbLoad,
    images: preloadImages,
    fonts: true
  });
  const [imageLoadingProgress, setImageLoadingProgress] = useState({
    loaded: 0,
    total: 0,
    failed: 0
  });

  useEffect(() => {
    const loadResources = async () => {
      const promises: Promise<void>[] = [];

      // Simulate database loading
      if (simulateDbLoad) {
        promises.push(
          new Promise((resolve) => {
            const dbLoadTime = Math.random() * (maxLoadTime - minLoadTime) + minLoadTime;
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, database: false }));
              resolve();
            }, dbLoadTime);
          })
        );
      }

      // Real image preloading using utility
      if (preloadImages && ALL_IMAGES.length > 0) {
        promises.push(
          new Promise((resolve) => {
            const totalImages = ALL_IMAGES.length;
            setImageLoadingProgress({ loaded: 0, total: totalImages, failed: 0 });

            console.log(`🖼️ Starting to preload ${totalImages} images...`);

            preloadImages(
              ALL_IMAGES,
              (loaded, total, failed) => {
                setImageLoadingProgress({ loaded, total, failed });
                console.log(`📊 Image progress: ${loaded}/${total} loaded, ${failed} failed`);
              }
            ).then(({ loaded, failed }) => {
              console.log(`✅ Image preloading complete: ${loaded.length} loaded, ${failed.length} failed`);
              setLoadingSteps(prev => ({ ...prev, images: false }));
              resolve();
            }).catch((error) => {
              console.error('❌ Image preloading error:', error);
              setLoadingSteps(prev => ({ ...prev, images: false }));
              resolve();
            });
          })
        );
      }

      // Load fonts (real check)
      promises.push(
        new Promise((resolve) => {
          if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            });
          } else {
            // Fallback for browsers without font loading API
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            }, 500);
          }
        })
      );

      // Wait for all resources to load
      await Promise.all(promises);
      
      // Small delay to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 200);
    };

    loadResources();
  }, [simulateDbLoad, preloadImages, minLoadTime, maxLoadTime]);

  const isStillLoading = Object.values(loadingSteps).some(Boolean) || isLoading;

  return {
    isLoading: isStillLoading,
    loadingSteps,
    imageProgress: imageLoadingProgress,
    progress: {
      database: !loadingSteps.database,
      images: !loadingSteps.images,
      fonts: !loadingSteps.fonts,
      overall: Math.round(
        ((!loadingSteps.database ? 1 : 0) +
         (!loadingSteps.images ? 1 : 0) +
         (!loadingSteps.fonts ? 1 : 0)) / 3 * 100
      )
    }
  };
};
