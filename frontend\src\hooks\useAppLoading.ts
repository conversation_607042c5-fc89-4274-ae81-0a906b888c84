import { useState, useEffect } from 'react';
import { ALL_IMAGES, preloadImages as preloadImagesUtil } from '../utils/imagePreloader';

interface UseAppLoadingOptions {
  simulateDbLoad?: boolean;
  preloadImages?: boolean;
  minLoadTime?: number;
  maxLoadTime?: number;
  imagesToPreload?: string[];
}



export const useAppLoading = ({
  simulateDbLoad = true,
  preloadImages = true,
  minLoadTime = 1000,
  maxLoadTime = 3000,
  imagesToPreload = ALL_IMAGES.map(img => img.src)
}: UseAppLoadingOptions = {}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingSteps, setLoadingSteps] = useState({
    database: simulateDbLoad,
    images: preloadImages,
    fonts: true
  });
  const [imageLoadingProgress, setImageLoadingProgress] = useState({
    loaded: 0,
    total: 0,
    failed: 0
  });

  useEffect(() => {
    const loadResources = async () => {
      const promises: Promise<void>[] = [];

      // Simulate database loading
      if (simulateDbLoad) {
        promises.push(
          new Promise((resolve) => {
            const dbLoadTime = Math.random() * (maxLoadTime - minLoadTime) + minLoadTime;
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, database: false }));
              resolve();
            }, dbLoadTime);
          })
        );
      }

      // Real image preloading using utility
      if (preloadImages && ALL_IMAGES.length > 0) {
        promises.push(
          new Promise((resolve) => {
            const totalImages = ALL_IMAGES.length;
            setImageLoadingProgress({ loaded: 0, total: totalImages, failed: 0 });

            console.log(`🖼️ Starting to preload ${totalImages} images...`);

            preloadImagesUtil(
              ALL_IMAGES,
              (loaded, total, failed) => {
                setImageLoadingProgress({ loaded, total, failed });
                console.log(`📊 Image progress: ${loaded}/${total} loaded, ${failed} failed`);

                // Log which images are being loaded
                if (loaded > 0) {
                  console.log(`🖼️ Recently loaded images:`, ALL_IMAGES.slice(0, loaded).map(img => img.src));
                }
              }
            ).then(({ loaded, failed }) => {
              console.log(`✅ Image preloading complete: ${loaded.length} loaded, ${failed.length} failed`);
              console.log(`🎯 All cached images:`, loaded.map(img => img.src));

              if (failed.length > 0) {
                console.warn(`⚠️ Failed to load images:`, failed);
              }

              setLoadingSteps(prev => ({ ...prev, images: false }));
              resolve();
            }).catch((error) => {
              console.error('❌ Image preloading error:', error);
              setLoadingSteps(prev => ({ ...prev, images: false }));
              resolve();
            });
          })
        );
      }

      // Load fonts (real check)
      promises.push(
        new Promise((resolve) => {
          if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            });
          } else {
            // Fallback for browsers without font loading API
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            }, 500);
          }
        })
      );

      // Wait for all resources to load
      await Promise.all(promises);
      
      // Small delay to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 200);
    };

    loadResources();
  }, [simulateDbLoad, preloadImages, minLoadTime, maxLoadTime]);

  const isStillLoading = Object.values(loadingSteps).some(Boolean) || isLoading;

  return {
    isLoading: isStillLoading,
    loadingSteps,
    imageProgress: imageLoadingProgress,
    progress: {
      database: !loadingSteps.database,
      images: !loadingSteps.images,
      fonts: !loadingSteps.fonts,
      overall: Math.round(
        ((!loadingSteps.database ? 1 : 0) +
         (!loadingSteps.images ? 1 : 0) +
         (!loadingSteps.fonts ? 1 : 0)) / 3 * 100
      )
    }
  };
};
