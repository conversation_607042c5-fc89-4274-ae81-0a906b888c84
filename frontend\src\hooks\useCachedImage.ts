import { useState, useEffect } from 'react';
import { getCachedImage, preloadImage, isImageCached } from '../utils/imagePreloader';

interface UseCachedImageOptions {
  fallback?: string;
  loadIfNotCached?: boolean;
}

interface UseCachedImageReturn {
  src: string;
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  cachedImage: HTMLImageElement | null;
}

/**
 * Hook to use cached images that were preloaded during app initialization
 * This ensures images don't reload when components mount
 */
export const useCachedImage = (
  originalSrc: string,
  options: UseCachedImageOptions = {}
): UseCachedImageReturn => {
  const { fallback, loadIfNotCached = true } = options;
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cachedImage, setCachedImage] = useState<HTMLImageElement | null>(null);

  useEffect(() => {
    if (!originalSrc) return;

    // Check if image is already cached
    const cached = getCachedImage(originalSrc);
    if (cached) {
      setCachedImage(cached);
      setError(null);
      return;
    }

    // If not cached and we should load it
    if (loadIfNotCached) {
      setIsLoading(true);
      setError(null);
      
      preloadImage(originalSrc)
        .then((img) => {
          setCachedImage(img);
          setIsLoading(false);
        })
        .catch((err) => {
          setError(err.message);
          setIsLoading(false);
          
          // Try fallback if available
          if (fallback) {
            preloadImage(fallback)
              .then((img) => {
                setCachedImage(img);
              })
              .catch(() => {
                // Fallback also failed, use original src
              });
          }
        });
    }
  }, [originalSrc, fallback, loadIfNotCached]);

  return {
    src: cachedImage ? cachedImage.src : (fallback || originalSrc),
    isLoaded: !!cachedImage,
    isLoading,
    error,
    cachedImage
  };
};

/**
 * Hook to check if multiple images are cached
 */
export const useMultipleCachedImages = (sources: string[]) => {
  const [cachedStatus, setCachedStatus] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const status: Record<string, boolean> = {};
    sources.forEach(src => {
      status[src] = isImageCached(src);
    });
    setCachedStatus(status);
  }, [sources]);

  return cachedStatus;
};

/**
 * Hook to preload an image on demand
 */
export const useImagePreloader = () => {
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const preload = async (src: string) => {
    if (isImageCached(src)) {
      return getCachedImage(src);
    }

    setLoading(prev => ({ ...prev, [src]: true }));
    setErrors(prev => ({ ...prev, [src]: '' }));

    try {
      const img = await preloadImage(src);
      setLoading(prev => ({ ...prev, [src]: false }));
      return img;
    } catch (error) {
      setLoading(prev => ({ ...prev, [src]: false }));
      setErrors(prev => ({ ...prev, [src]: error.message }));
      throw error;
    }
  };

  return {
    preload,
    loading,
    errors
  };
};

export default useCachedImage;
