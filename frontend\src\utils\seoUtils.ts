// SEO utility functions for better search engine optimization

export const generateBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  }
}

export const generateServiceSchema = (service: {
  name: string
  description: string
  provider: string
  areaServed: string
  serviceType: string
  url: string
}) => {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": service.name,
    "description": service.description,
    "provider": {
      "@type": "Organization",
      "name": service.provider
    },
    "areaServed": service.areaServed,
    "serviceType": service.serviceType,
    "url": service.url
  }
}

export const generateArticleSchema = (article: {
  headline: string
  description: string
  author: string
  datePublished: string
  dateModified: string
  image: string
  url: string
}) => {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.headline,
    "description": article.description,
    "author": {
      "@type": "Person",
      "name": article.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "Delta Xero Creations",
      "logo": {
        "@type": "ImageObject",
        "url": "https://deltaxerocreations.com/logo.png"
      }
    },
    "datePublished": article.datePublished,
    "dateModified": article.dateModified,
    "image": article.image,
    "url": article.url
  }
}

export const generateFAQSchema = (faqs: Array<{ question: string; answer: string }>) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}

export const generateLocalBusinessSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Delta Xero Creations",
    "description": "Affordable web and app development services",
    "url": "https://deltaxerocreations.com",
    "telephone": "+1-XXX-XXX-XXXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "40.7128",
      "longitude": "-74.0060"
    },
    "openingHours": "Mo-Fr 09:00-18:00",
    "priceRange": "$$",
    "paymentAccepted": "Cash, Credit Card, PayPal",
    "currenciesAccepted": "USD"
  }
}

export const generateJobPostingSchema = (job: {
  title: string
  description: string
  datePosted: string
  validThrough: string
  employmentType: string
  location: string
  baseSalary?: {
    currency: string
    value: {
      minValue: number
      maxValue: number
    }
  }
}) => {
  return {
    "@context": "https://schema.org",
    "@type": "JobPosting",
    "title": job.title,
    "description": job.description,
    "datePosted": job.datePosted,
    "validThrough": job.validThrough,
    "employmentType": job.employmentType,
    "hiringOrganization": {
      "@type": "Organization",
      "name": "Delta Xero Creations",
      "sameAs": "https://deltaxerocreations.com"
    },
    "jobLocation": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": job.location,
        "addressCountry": "US"
      }
    },
    ...(job.baseSalary && {
      "baseSalary": {
        "@type": "MonetaryAmount",
        "currency": job.baseSalary.currency,
        "value": {
          "@type": "QuantitativeValue",
          "minValue": job.baseSalary.value.minValue,
          "maxValue": job.baseSalary.value.maxValue,
          "unitText": "YEAR"
        }
      }
    })
  }
}

// Function to add structured data to page
export const addStructuredData = (data: object, id: string) => {
  // Remove existing script with same id
  const existingScript = document.querySelector(`script[data-schema="${id}"]`)
  if (existingScript) {
    existingScript.remove()
  }

  // Add new script
  const script = document.createElement('script')
  script.setAttribute('type', 'application/ld+json')
  script.setAttribute('data-schema', id)
  script.textContent = JSON.stringify(data)
  document.head.appendChild(script)
}

// Function to optimize images for SEO
export const optimizeImageForSEO = (src: string, alt: string, title?: string) => {
  return {
    src,
    alt,
    title: title || alt,
    loading: 'lazy' as const,
    decoding: 'async' as const
  }
}

// Function to generate meta description from content
export const generateMetaDescription = (content: string, maxLength: number = 160): string => {
  // Remove HTML tags
  const textContent = content.replace(/<[^>]*>/g, '')
  
  // Truncate to maxLength
  if (textContent.length <= maxLength) {
    return textContent
  }
  
  // Find the last complete word within the limit
  const truncated = textContent.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')
  
  return lastSpaceIndex > 0 
    ? truncated.substring(0, lastSpaceIndex) + '...'
    : truncated + '...'
}

// Function to generate keywords from content
export const generateKeywords = (content: string, existingKeywords: string[] = []): string[] => {
  // Simple keyword extraction (in a real app, you might use a more sophisticated NLP library)
  const words = content.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3)
  
  // Count word frequency
  const wordCount: Record<string, number> = {}
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1
  })
  
  // Get most frequent words
  const sortedWords = Object.entries(wordCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word)
  
  // Combine with existing keywords and remove duplicates
  return [...new Set([...existingKeywords, ...sortedWords])]
}

// Function to validate SEO requirements
export const validateSEO = (data: {
  title?: string
  description?: string
  keywords?: string
  image?: string
}) => {
  const issues: string[] = []
  
  if (!data.title) {
    issues.push('Title is missing')
  } else if (data.title.length > 60) {
    issues.push('Title is too long (should be under 60 characters)')
  } else if (data.title.length < 30) {
    issues.push('Title is too short (should be at least 30 characters)')
  }
  
  if (!data.description) {
    issues.push('Meta description is missing')
  } else if (data.description.length > 160) {
    issues.push('Meta description is too long (should be under 160 characters)')
  } else if (data.description.length < 120) {
    issues.push('Meta description is too short (should be at least 120 characters)')
  }
  
  if (!data.keywords) {
    issues.push('Keywords are missing')
  }
  
  if (!data.image) {
    issues.push('Open Graph image is missing')
  }
  
  return {
    isValid: issues.length === 0,
    issues
  }
}
