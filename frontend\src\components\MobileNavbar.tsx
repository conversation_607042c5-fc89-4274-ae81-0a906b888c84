import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileNavbarProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNavbar = ({ isOpen, onClose }: MobileNavbarProps) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  useEffect(() => {
    document.body.style.overflow = isOpen ? 'hidden' : 'unset';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const navigationData = {
    solutions: [
      { name: 'Web Development', path: '/web-development' },
      { name: 'Mobile Development', path: '/mobile-development' },
      { name: 'API Development', path: '/api-development' },
      { name: 'UI/UX Design', path: '/ui-ux-design' },
      { name: 'Digital Transformation', path: '/digital-transformation' },
      { name: 'Brand Strategy', path: '/brand-strategy' },
    ],
    industries: [
      { name: 'E-commerce', path: '/ecommerce' },
      { name: 'SaaS Platforms', path: '/saas' },
      { name: 'Education', path: '/education' },
      { name: 'Healthcare', path: '/healthcare' },
      { name: 'Fintech', path: '/fintech' },
      { name: 'Banking', path: '/banking' },
    ],
    company: [
      { name: 'About Us', path: '/about' },
      { name: 'Careers', path: '/jobs' },
      { name: 'Blog', path: '/blog' },
      { name: 'Get Quote', path: '/get-quote' },
    ],
  };

  const handleLinkClick = () => {
    setActiveSection(null);
    onClose();
  };

  const toggleSection = (section: string) => {
    setActiveSection((prev) => (prev === section ? null : section));
  };

  const renderLinks = (section: string) => (
    <motion.div
      initial={{ height: 0, opacity: 0, y: -10 }}
      animate={{ height: 'auto', opacity: 1, y: 0 }}
      exit={{ height: 0, opacity: 0, y: -10 }}
      transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
      className="overflow-hidden"
    >
      <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-2xl p-6 mt-4 backdrop-blur-sm border border-white/10">
        <div
          className={
            section === 'industries'
              ? 'grid grid-cols-2 gap-3'
              : 'grid grid-cols-1 gap-3'
          }
        >
          {navigationData[section as keyof typeof navigationData].map((item, index) => (
            <motion.div
              key={index}
              initial={{ x: -10, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
            >
              <Link
                to={item.path}
                onClick={handleLinkClick}
                className="group flex items-center space-x-3 text-white/80 hover:text-primary-400 py-3 px-4 rounded-xl hover:bg-white/5 transition-all duration-300 touch-manipulation"
              >
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary-400 to-primary-500 opacity-60 group-hover:opacity-100 transition-opacity"></div>
                <span className="text-base font-medium group-hover:translate-x-1 transition-transform duration-300">
                  {item.name}
                </span>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-md z-40 lg:hidden"
            onClick={onClose}
          />

          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
            className="fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-black z-50 p-6 overflow-y-auto lg:hidden"
          >
            <div className="flex justify-between items-center mb-6">
              <Link to="/" onClick={handleLinkClick} className="flex items-center space-x-2">
                <img
                  src="https://sdwgyjjcxdhdlcuvjadq.supabase.co/storage/v1/object/public/invoices//delta_zero_vertical_logo-removebg-preview.png"
                  alt="Delta Xero Creations"
                  className="w-32 object-contain"
                />
                <div>
                  <h1 className="text-white text-xl font-bold leading-tight">Delta Xero</h1>
                  <p className="text-xs text-gray-400 font-medium tracking-wider">CREATIONS</p>
                </div>
              </Link>
              <button
                onClick={onClose}
                className="text-white hover:text-red-400 transition p-2 rounded-full hover:bg-white/10"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Services standalone link */}
              <Link
                to="/services"
                onClick={handleLinkClick}
                className="block text-white text-lg font-semibold hover:text-primary-400 transition py-2"
              >
                Services
              </Link>

              {['solutions', 'industries', 'company'].map((section) => (
                <div key={section} className="space-y-2">
                  <button
                    onClick={() => toggleSection(section)}
                    className="w-full flex justify-between items-center text-left text-white text-lg font-semibold hover:text-primary-400 transition"
                  >
                    <span className="capitalize">{section}</span>
                    <ChevronRight
                      className={cn(
                        'transition-transform',
                        activeSection === section ? 'rotate-90' : ''
                      )}
                    />
                  </button>
                  <AnimatePresence>
                    {activeSection === section && renderLinks(section)}
                  </AnimatePresence>
                </div>
              ))}

              <Link
                to="/get-quote"
                onClick={handleLinkClick}
                className="block mt-10 text-center py-4 px-6 rounded-xl bg-gradient-to-r from-primary-500 to-primary-700 text-white font-semibold text-lg hover:shadow-lg hover:scale-105 transition"
              >
                Get a Quote
              </Link>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MobileNavbar;
