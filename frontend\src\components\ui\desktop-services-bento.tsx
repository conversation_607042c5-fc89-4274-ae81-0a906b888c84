import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Service {
  icon: React.ComponentType<{ className?: string; size?: number }>;
  title: string;
  description: string;
  features: string[];
  color: string;
  link?: string;
}

interface DesktopServicesBentoProps {
  services: Service[];
  className?: string;
}

export const DesktopServicesBento: React.FC<DesktopServicesBentoProps> = ({ services, className }) => {
  // Define layout patterns for different service counts
  const getGridLayout = () => {
    if (services.length === 4) {
      return {
        gridClass: "grid-cols-4 grid-rows-1",
        cards: [
          { size: 'large', position: 'col-span-2 row-span-2' }, // Hero card (left side)
          { size: 'medium', position: 'col-span-2 row-span-1' }, // Top right (wide)
          { size: 'medium', position: 'col-span-1 row-span-1' }, // Bottom right 1
          { size: 'medium', position: 'col-span-1 row-span-1' }, // Bottom right 2
        ]
      };
    }
    // Default 2x2 grid for other counts
    return {
      gridClass: "grid-cols-2 grid-rows-2",
      cards: services.map((_, index) => ({
        size: 'medium',
        position: 'col-span-1 row-span-1'
      }))
    };
  };

  const { gridClass, cards } = getGridLayout();

  return (
    <div className={cn(
      "hidden lg:grid gap-4 xl:gap-6 auto-rows-[180px] xl:auto-rows-[200px] max-w-7xl mx-auto",
      gridClass,
      className
    )}>
      {services.map((service, index) => {
        const Icon = service.icon;
        const layoutConfig = cards[index] || { size: 'medium', position: 'col-span-1 row-span-1' };
        const CardWrapper = service.link ? Link : 'div';
        const cardProps = service.link ? { to: service.link } : {};
        
        return (
          <CardWrapper key={index} {...cardProps} className={service.link ? "block" : ""}>
            <motion.div
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ 
                duration: 0.8, 
                delay: index * 0.15,
                type: "spring",
                stiffness: 100,
                damping: 20
              }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
              className={cn(
                layoutConfig.position,
                "group relative overflow-hidden rounded-2xl xl:rounded-3xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/80 backdrop-blur-xl hover:border-white/20 transition-all duration-700 cursor-pointer shadow-xl hover:shadow-2xl"
              )}
            >
              {/* Background gradient overlay */}
              <div className={cn(
                "absolute inset-0 bg-gradient-to-br opacity-10 group-hover:opacity-20 transition-opacity duration-700",
                service.color
              )} />

              {/* Animated background pattern */}
              <motion.div
                className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-700"
                animate={{
                  background: [
                    "radial-gradient(circle at 30% 40%, rgba(120,119,198,0.3), transparent 50%)",
                    "radial-gradient(circle at 70% 60%, rgba(120,119,198,0.3), transparent 50%)",
                    "radial-gradient(circle at 30% 40%, rgba(120,119,198,0.3), transparent 50%)"
                  ]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <div className="absolute top-0 left-0 w-full h-full bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.02)_50%,transparent_75%)]" />
              </motion.div>

              {/* Floating particles effect for large cards */}
              {layoutConfig.size === 'large' && (
                <div className="absolute inset-0 overflow-hidden">
                  {[...Array(6)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-white/20 rounded-full"
                      style={{
                        left: `${20 + i * 15}%`,
                        top: `${30 + (i % 2) * 40}%`,
                      }}
                      animate={{
                        y: [-10, 10, -10],
                        opacity: [0.2, 0.5, 0.2],
                      }}
                      transition={{
                        duration: 3 + i * 0.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                  ))}
                </div>
              )}

              {/* Content */}
              <div className={cn(
                "relative z-10 h-full flex flex-col justify-between",
                layoutConfig.size === 'large' ? 'p-8' : layoutConfig.size === 'wide' ? 'p-6' : 'p-5'
              )}>
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className={cn(
                    "rounded-2xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl",
                    service.color,
                    layoutConfig.size === 'large' ? 'w-16 h-16 rounded-3xl' : 'w-12 h-12'
                  )}>
                    <Icon className={cn(
                      "text-white",
                      layoutConfig.size === 'large' ? 'w-8 h-8' : 'w-6 h-6'
                    )} />
                  </div>
                  
                  {service.link && (
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      whileHover={{ scale: 1.1, rotate: -45 }}
                    >
                      <ArrowRight className="text-white/60 w-5 h-5" />
                    </motion.div>
                  )}
                </div>

                {/* Title and Description */}
                <div className="flex-1">
                  <h3 className={cn(
                    "text-white font-bold mb-3 group-hover:text-gray-100 transition-colors leading-tight",
                    layoutConfig.size === 'large' ? 'text-2xl mb-4' : layoutConfig.size === 'wide' ? 'text-xl' : 'text-lg'
                  )}>
                    {service.title}
                  </h3>
                  
                  <p className={cn(
                    "text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors",
                    layoutConfig.size === 'large' ? 'text-base mb-6' :
                    layoutConfig.position.includes('col-span-2') ? 'text-sm mb-4' : 'text-xs mb-3'
                  )}>
                    {layoutConfig.size === 'large'
                      ? service.description
                      : layoutConfig.position.includes('col-span-2')
                        ? service.description.length > 120
                          ? `${service.description.substring(0, 120)}...`
                          : service.description
                        : service.description.length > 80
                          ? `${service.description.substring(0, 80)}...`
                          : service.description
                    }
                  </p>
                </div>

                {/* Features */}
                {layoutConfig.size === 'large' && (
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {service.features.slice(0, 4).map((feature, idx) => (
                      <motion.div 
                        key={idx} 
                        className="flex items-center space-x-2"
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 + idx * 0.05 }}
                        viewport={{ once: true }}
                      >
                        <div className={cn(
                          "w-1.5 h-1.5 rounded-full bg-gradient-to-r",
                          service.color
                        )} />
                        <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors">
                          {feature}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                )}

                {/* Medium card features for non-large cards */}
                {layoutConfig.size === 'medium' && layoutConfig.position.includes('col-span-2') && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {service.features.slice(0, 3).map((feature, idx) => (
                      <span
                        key={idx}
                        className="px-3 py-1 bg-white/10 rounded-full text-gray-300 border border-white/20 text-xs group-hover:bg-white/15 transition-colors"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                )}

                {/* Bottom accent */}
                <div className="flex items-center justify-between">
                  {layoutConfig.size === 'large' && (
                    <div className="flex items-center space-x-2">
                      <Sparkles className="w-4 h-4 text-primary-400" />
                      <span className="text-xs text-primary-400 font-medium">Premium Service</span>
                    </div>
                  )}
                  
                  <div className={cn(
                    "h-1 bg-gradient-to-r rounded-full opacity-60 group-hover:opacity-100 transition-opacity",
                    service.color,
                    layoutConfig.size === 'large' ? 'w-20' : 'w-12 ml-auto'
                  )} />
                </div>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
              
              {/* Border glow effect */}
              <div className={cn(
                "absolute inset-0 rounded-3xl bg-gradient-to-br opacity-0 group-hover:opacity-40 transition-opacity duration-700 blur-2xl -z-10",
                service.color
              )} />
            </motion.div>
          </CardWrapper>
        );
      })}
    </div>
  );
};
