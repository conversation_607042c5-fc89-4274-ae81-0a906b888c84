import React, { useState, useEffect } from "react";
import {
  motion,
  useScroll,
  useTransform,
  useSpring,
} from "framer-motion";
import { CachedImage } from "../CachedImage";

export const HeroParallax = ({ products }) => {
  const firstRow = products.slice(0, 5);
  const secondRow = products.slice(5, 10);
  const thirdRow = products.slice(10, 15);
  const ref = React.useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
    layoutEffect: false, // Use useEffect instead of useLayoutEffect for better performance
  });

  // Mobile-optimized spring config - less intensive
  const springConfig = isMobile
    ? { stiffness: 60, damping: 50, bounce: 0, mass: 1.2 }
    : { stiffness: 80, damping: 40, bounce: 0, mass: 1 };

  // Reduce animation complexity on mobile
  const translateDistance = isMobile ? 300 : 600;
  const rotateAmount = isMobile ? 8 : 15;
  const rotateZAmount = isMobile ? 10 : 20;

  const translateX = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, translateDistance]),
    springConfig
  );
  const translateXReverse = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -translateDistance]),
    springConfig
  );
  const rotateX = useSpring(
    useTransform(scrollYProgress, [0, 0.3], [rotateAmount, 0]),
    springConfig
  );
  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.3], [0.4, 1]),
    springConfig
  );
  const rotateZ = useSpring(
    useTransform(scrollYProgress, [0, 0.3], [rotateZAmount, 0]),
    springConfig
  );
  const translateY = useSpring(
    useTransform(scrollYProgress, [0, 0.3], [-200, 150]),
    springConfig
  );

  return (
    <div
      ref={ref}
      className="h-[160vh] md:h-[250vh] py-8 md:py-16 overflow-hidden antialiased relative flex flex-col self-auto [perspective:1000px] [transform-style:preserve-3d] bg-dark-950 gpu-accelerated"
    >
      <Header />
      <motion.div
        initial={{
          rotateX: 15,
          rotateZ: 20,
          translateY: -160,
          opacity: 0.2,
        }}
        style={{
          rotateX,
          rotateZ,
          translateY,
          opacity,
          willChange: 'transform, opacity',
          backfaceVisibility: 'hidden',
          transform: 'translateZ(0)', // Force hardware acceleration
        }}
        className=""
      >
        <motion.div
          className={`flex flex-row-reverse space-x-reverse mb-12 ${isMobile ? 'space-x-6' : 'space-x-12'}`}
          style={{
            willChange: 'transform',
            backfaceVisibility: 'hidden',
          }}
        >
          {firstRow.map((product) => (
            <ProductCard
              product={product}
              translate={isMobile ? 0 : translateX}
              key={product.title}
              isMobile={isMobile}
            />
          ))}
        </motion.div>
        <motion.div
          className={`flex flex-row mb-12 ${isMobile ? 'space-x-6' : 'space-x-12'}`}
          style={{
            willChange: 'transform',
            backfaceVisibility: 'hidden',
          }}
        >
          {secondRow.map((product) => (
            <ProductCard
              product={product}
              translate={isMobile ? 0 : translateXReverse}
              key={product.title}
              isMobile={isMobile}
            />
          ))}
        </motion.div>
        <motion.div
          className={`flex flex-row-reverse space-x-reverse ${isMobile ? 'space-x-6' : 'space-x-12'}`}
          style={{
            willChange: 'transform',
            backfaceVisibility: 'hidden',
          }}
        >
          {thirdRow.map((product) => (
            <ProductCard
              product={product}
              translate={isMobile ? 0 : translateX}
              key={product.title}
              isMobile={isMobile}
            />
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export const Header = () => {
  return (
    <div className="max-w-7xl relative mx-auto py-8 md:py-12 lg:py-20 px-4 w-full left-0 top-[10vh] md:top-0">
      <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight w-[90%]">
        <span className="bg-gradient-to-r from-primary-400 via-primary-500 to-primary-800 bg-clip-text md:text-transparent">Human Ingenuity.</span> <span className="bg-gradient-to-r from-blue-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent">AI Power.</span> <br className="hidden sm:block" /> Unmatched Digital Experiences.
      </h1>
      <p className="max-w-2xl text-sm sm:text-base md:text-lg lg:text-xl mt-4 md:mt-6 text-gray-300 leading-relaxed">
        Where creativity meets cutting-edge technology to deliver transformational digital solutions through affordable excellence.
        We build beautiful products with the latest technologies and frameworks.
      </p>
    </div>
  );
};

export const ProductCard = ({ product, translate, isMobile = false }) => {
  // Coming Soon placeholder component
  const ComingSoonCard = () => (
    <div className="w-full h-full bg-gradient-to-br from-dark-800 to-dark-900 rounded-lg flex flex-col items-center justify-center border border-gray-700/50 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-400/20 to-primary-600/20"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center">
        <div className="text-5xl mb-6 opacity-60 group-hover/product:scale-110 transition-transform duration-300">🚀</div>
        <h3 className="text-white font-semibold text-xl mb-3 group-hover/product:text-primary-300 transition-colors">{product.title}</h3>
        <p className="text-gray-400 text-sm mb-4 px-4">Coming Soon</p>
        <div className="w-20 h-1 bg-gradient-to-r from-primary-400 to-primary-600 rounded-full mx-auto opacity-70 group-hover/product:opacity-100 transition-opacity"></div>
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-primary-600/10 opacity-0 group-hover/product:opacity-100 transition-opacity duration-300 rounded-lg"></div>
    </div>
  );

  return (
    <motion.div
      style={{
        x: translate,
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)', // Force hardware acceleration
      }}
      whileHover={isMobile ? {} : {
        y: -20,
      }}
      transition={{
        type: "tween",
        duration: isMobile ? 0.2 : 0.3,
        ease: "easeOut"
      }}
      key={product.title}
      className={`group/product relative flex-shrink-0 ${
        isMobile
          ? 'h-48 w-64 sm:h-56 sm:w-72'
          : 'h-64 w-80 sm:h-80 sm:w-96 md:h-96 md:w-[30rem]'
      }`}
    >
      {product.isProject ? (
        // Real project card
        <>
          <a
            href={product.link}
            target="_blank"
            rel="noopener noreferrer"
            className="block group-hover/product:shadow-2xl"
          >
            <CachedImage
              src={product.thumbnail}
              className="object-cover object-center absolute h-full w-full inset-0 rounded-lg"
              alt={product.title}
              loading="lazy"
              decoding="async"
              loadIfNotCached={true}
              style={{
                willChange: 'transform',
                backfaceVisibility: 'hidden',
                transform: 'translateZ(0)', // Force hardware acceleration
              }}
            />
          </a>
          <div className="absolute inset-0 h-full w-full opacity-0 group-hover/product:opacity-80 bg-black pointer-events-none rounded-lg"></div>
          <h2 className="absolute bottom-4 left-4 opacity-0 group-hover/product:opacity-100 text-white font-semibold">
            {product.title}
          </h2>
        </>
      ) : (
        // Coming soon placeholder
        <ComingSoonCard />
      )}
    </motion.div>
  );
};
