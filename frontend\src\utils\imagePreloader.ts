// Image preloader utility for collecting and preloading all app images

export interface ImageSource {
  src: string;
  alt?: string;
  priority?: 'high' | 'medium' | 'low';
  category?: string;
}

// Critical images that should be loaded first
export const CRITICAL_IMAGES: ImageSource[] = [
  // Logo and branding
  {
    src: 'https://ik.imagekit.io/buildwithangga/logo-deltaxero.png',
    alt: 'Delta Xero Creations Logo',
    priority: 'high',
    category: 'branding'
  },
  
  // Hero/background images
  {
    src: '/og-home.jpg',
    alt: 'Home page hero image',
    priority: 'high',
    category: 'hero'
  },
  {
    src: '/og-about.jpg',
    alt: 'About page hero image',
    priority: 'medium',
    category: 'hero'
  },
  {
    src: '/og-services.jpg',
    alt: 'Services page hero image',
    priority: 'medium',
    category: 'hero'
  }
];

// Portfolio/project images
export const PORTFOLIO_IMAGES: ImageSource[] = [
  {
    src: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
    alt: 'E-commerce project showcase',
    priority: 'medium',
    category: 'portfolio'
  },
  {
    src: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop',
    alt: 'Mobile app project showcase',
    priority: 'medium',
    category: 'portfolio'
  },
  {
    src: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',
    alt: 'Web development project showcase',
    priority: 'medium',
    category: 'portfolio'
  }
];

// Technology/company logos
export const TECH_LOGOS: ImageSource[] = [
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    alt: 'React logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    alt: 'Node.js logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    alt: 'TypeScript logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    alt: 'Next.js logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg',
    alt: 'MongoDB logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    alt: 'PostgreSQL logo',
    priority: 'low',
    category: 'tech'
  }
];

// Combine all images
export const ALL_IMAGES: ImageSource[] = [
  ...CRITICAL_IMAGES,
  ...PORTFOLIO_IMAGES,
  ...TECH_LOGOS
];

// Get images by priority
export const getImagesByPriority = (priority: 'high' | 'medium' | 'low'): ImageSource[] => {
  return ALL_IMAGES.filter(img => img.priority === priority);
};

// Get images by category
export const getImagesByCategory = (category: string): ImageSource[] => {
  return ALL_IMAGES.filter(img => img.category === category);
};

// Preload a single image
export const preloadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
    
    // Set timeout for slow loading images
    setTimeout(() => {
      if (!img.complete) {
        reject(new Error(`Timeout loading image: ${src}`));
      }
    }, 10000);
    
    img.src = src;
  });
};

// Preload multiple images with progress tracking
export const preloadImages = (
  images: ImageSource[],
  onProgress?: (loaded: number, total: number, failed: number) => void
): Promise<{ loaded: HTMLImageElement[], failed: string[] }> => {
  return new Promise((resolve) => {
    const total = images.length;
    let loaded = 0;
    let failed = 0;
    const loadedImages: HTMLImageElement[] = [];
    const failedImages: string[] = [];
    
    if (total === 0) {
      resolve({ loaded: loadedImages, failed: failedImages });
      return;
    }
    
    const checkComplete = () => {
      if (loaded + failed >= total) {
        resolve({ loaded: loadedImages, failed: failedImages });
      }
    };
    
    images.forEach((imageSource) => {
      preloadImage(imageSource.src)
        .then((img) => {
          loaded++;
          loadedImages.push(img);
          onProgress?.(loaded, total, failed);
          checkComplete();
        })
        .catch(() => {
          failed++;
          failedImages.push(imageSource.src);
          onProgress?.(loaded, total, failed);
          checkComplete();
        });
    });
  });
};

// Preload images in priority order
export const preloadImagesByPriority = async (
  onProgress?: (loaded: number, total: number, failed: number, priority: string) => void
): Promise<{ loaded: HTMLImageElement[], failed: string[] }> => {
  const priorities: Array<'high' | 'medium' | 'low'> = ['high', 'medium', 'low'];
  const allLoaded: HTMLImageElement[] = [];
  const allFailed: string[] = [];
  
  for (const priority of priorities) {
    const images = getImagesByPriority(priority);
    if (images.length === 0) continue;
    
    const result = await preloadImages(images, (loaded, total, failed) => {
      onProgress?.(
        allLoaded.length + loaded,
        ALL_IMAGES.length,
        allFailed.length + failed,
        priority
      );
    });
    
    allLoaded.push(...result.loaded);
    allFailed.push(...result.failed);
  }
  
  return { loaded: allLoaded, failed: allFailed };
};

// Auto-detect images from DOM (for additional images not in the predefined lists)
export const detectImagesFromDOM = (): string[] => {
  const images = document.querySelectorAll('img');
  const imageSources: string[] = [];
  
  images.forEach((img) => {
    if (img.src && !imageSources.includes(img.src)) {
      imageSources.push(img.src);
    }
    
    // Also check data-src for lazy loaded images
    const dataSrc = img.getAttribute('data-src');
    if (dataSrc && !imageSources.includes(dataSrc)) {
      imageSources.push(dataSrc);
    }
  });
  
  return imageSources;
};

// Get all unique image sources
export const getAllImageSources = (): string[] => {
  const predefinedSources = ALL_IMAGES.map(img => img.src);
  const domSources = detectImagesFromDOM();
  
  return [...new Set([...predefinedSources, ...domSources])];
};

// Utility to add new images to preload list
export const addImagesToPreload = (newImages: ImageSource[]): void => {
  ALL_IMAGES.push(...newImages);
};

// Clear browser image cache (useful for development)
export const clearImageCache = (): void => {
  if ('caches' in window) {
    caches.keys().then((names) => {
      names.forEach((name) => {
        if (name.includes('image')) {
          caches.delete(name);
        }
      });
    });
  }
};
