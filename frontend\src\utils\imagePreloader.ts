// Image preloader utility for collecting and preloading all app images

export interface ImageSource {
  src: string;
  alt?: string;
  priority?: 'high' | 'medium' | 'low';
  category?: string;
}

// Global image cache to store preloaded images
const imageCache = new Map<string, HTMLImageElement>();
const loadingPromises = new Map<string, Promise<HTMLImageElement>>();

// Critical images that should be loaded first
export const CRITICAL_IMAGES: ImageSource[] = [
  // Logo and branding
  {
    src: 'https://ik.imagekit.io/buildwithangga/logo-deltaxero.png',
    alt: 'Delta Xero Creations Logo',
    priority: 'high',
    category: 'branding'
  },
  
  // Hero/background images
  {
    src: '/og-home.jpg',
    alt: 'Home page hero image',
    priority: 'high',
    category: 'hero'
  },
  {
    src: '/og-about.jpg',
    alt: 'About page hero image',
    priority: 'medium',
    category: 'hero'
  },
  {
    src: '/og-services.jpg',
    alt: 'Services page hero image',
    priority: 'medium',
    category: 'hero'
  }
];

// Import project images to get the correct Vite URLs
import CRMImage from '../components/ui/project images/CRM.png';
import CyberVisionImage from '../components/ui/project images/CyberVision.png';
import OmnexiImage from '../components/ui/project images/Omnexi solutions.png';
import ProjectManagementImage from '../components/ui/project images/Project manegement.png';
import TechStoreImage from '../components/ui/project images/TechStore.png';
import ZeltImage from '../components/ui/project images/Zelt.png';
import BhagwatiImage from '../components/ui/project images/bhagwati handlooms.png';
import InvoiceImage from '../components/ui/project images/invoice management.png';
import OchiImage from '../components/ui/project images/ochi.png';

// Portfolio/project images (using imported URLs)
export const PORTFOLIO_IMAGES: ImageSource[] = [
  {
    src: CRMImage,
    alt: 'CRM Dashboard project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: CyberVisionImage,
    alt: 'CyberVision Security project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: OmnexiImage,
    alt: 'Omnexi Solutions project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: ProjectManagementImage,
    alt: 'Project Management system',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: TechStoreImage,
    alt: 'TechStore E-commerce project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: ZeltImage,
    alt: 'Zelt project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: BhagwatiImage,
    alt: 'Bhagwati Handlooms project',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: InvoiceImage,
    alt: 'Invoice Management system',
    priority: 'high',
    category: 'portfolio'
  },
  {
    src: OchiImage,
    alt: 'Ochi project',
    priority: 'high',
    category: 'portfolio'
  }
];

// Technology/company logos
export const TECH_LOGOS: ImageSource[] = [
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    alt: 'React logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    alt: 'Node.js logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    alt: 'TypeScript logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    alt: 'Next.js logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg',
    alt: 'MongoDB logo',
    priority: 'low',
    category: 'tech'
  },
  {
    src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    alt: 'PostgreSQL logo',
    priority: 'low',
    category: 'tech'
  }
];

// Combine all images
export const ALL_IMAGES: ImageSource[] = [
  ...CRITICAL_IMAGES,
  ...PORTFOLIO_IMAGES,
  ...TECH_LOGOS
];

// Get images by priority
export const getImagesByPriority = (priority: 'high' | 'medium' | 'low'): ImageSource[] => {
  return ALL_IMAGES.filter(img => img.priority === priority);
};

// Get images by category
export const getImagesByCategory = (category: string): ImageSource[] => {
  return ALL_IMAGES.filter(img => img.category === category);
};

// Preload a single image with caching
export const preloadImage = (src: string): Promise<HTMLImageElement> => {
  // Check if image is already cached
  if (imageCache.has(src)) {
    return Promise.resolve(imageCache.get(src)!);
  }

  // Check if image is currently being loaded
  if (loadingPromises.has(src)) {
    return loadingPromises.get(src)!;
  }

  // Create new loading promise
  const loadingPromise = new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      // Cache the loaded image
      imageCache.set(src, img);
      loadingPromises.delete(src);
      console.log(`✅ Cached image: ${src}`);
      resolve(img);
    };

    img.onerror = () => {
      loadingPromises.delete(src);
      reject(new Error(`Failed to load image: ${src}`));
    };

    // Set timeout for slow loading images
    setTimeout(() => {
      if (!img.complete) {
        loadingPromises.delete(src);
        reject(new Error(`Timeout loading image: ${src}`));
      }
    }, 10000);

    img.src = src;
  });

  // Store the loading promise
  loadingPromises.set(src, loadingPromise);
  return loadingPromise;
};

// Preload multiple images with progress tracking
export const preloadImages = (
  images: ImageSource[],
  onProgress?: (loaded: number, total: number, failed: number) => void
): Promise<{ loaded: HTMLImageElement[], failed: string[] }> => {
  return new Promise((resolve) => {
    const total = images.length;
    let loaded = 0;
    let failed = 0;
    const loadedImages: HTMLImageElement[] = [];
    const failedImages: string[] = [];
    
    if (total === 0) {
      resolve({ loaded: loadedImages, failed: failedImages });
      return;
    }
    
    const checkComplete = () => {
      if (loaded + failed >= total) {
        resolve({ loaded: loadedImages, failed: failedImages });
      }
    };
    
    images.forEach((imageSource) => {
      preloadImage(imageSource.src)
        .then((img) => {
          loaded++;
          loadedImages.push(img);
          onProgress?.(loaded, total, failed);
          checkComplete();
        })
        .catch(() => {
          failed++;
          failedImages.push(imageSource.src);
          onProgress?.(loaded, total, failed);
          checkComplete();
        });
    });
  });
};

// Preload images in priority order
export const preloadImagesByPriority = async (
  onProgress?: (loaded: number, total: number, failed: number, priority: string) => void
): Promise<{ loaded: HTMLImageElement[], failed: string[] }> => {
  const priorities: Array<'high' | 'medium' | 'low'> = ['high', 'medium', 'low'];
  const allLoaded: HTMLImageElement[] = [];
  const allFailed: string[] = [];
  
  for (const priority of priorities) {
    const images = getImagesByPriority(priority);
    if (images.length === 0) continue;
    
    const result = await preloadImages(images, (loaded, total, failed) => {
      onProgress?.(
        allLoaded.length + loaded,
        ALL_IMAGES.length,
        allFailed.length + failed,
        priority
      );
    });
    
    allLoaded.push(...result.loaded);
    allFailed.push(...result.failed);
  }
  
  return { loaded: allLoaded, failed: allFailed };
};

// Auto-detect images from DOM (for additional images not in the predefined lists)
export const detectImagesFromDOM = (): string[] => {
  const images = document.querySelectorAll('img');
  const imageSources: string[] = [];
  
  images.forEach((img) => {
    if (img.src && !imageSources.includes(img.src)) {
      imageSources.push(img.src);
    }
    
    // Also check data-src for lazy loaded images
    const dataSrc = img.getAttribute('data-src');
    if (dataSrc && !imageSources.includes(dataSrc)) {
      imageSources.push(dataSrc);
    }
  });
  
  return imageSources;
};

// Get all unique image sources
export const getAllImageSources = (): string[] => {
  const predefinedSources = ALL_IMAGES.map(img => img.src);
  const domSources = detectImagesFromDOM();
  
  return [...new Set([...predefinedSources, ...domSources])];
};

// Utility to add new images to preload list
export const addImagesToPreload = (newImages: ImageSource[]): void => {
  ALL_IMAGES.push(...newImages);
};

// Get cached image
export const getCachedImage = (src: string): HTMLImageElement | null => {
  return imageCache.get(src) || null;
};

// Check if image is cached
export const isImageCached = (src: string): boolean => {
  return imageCache.has(src);
};

// Get cached image src for use in components
export const getCachedImageSrc = (src: string): string => {
  const cachedImg = imageCache.get(src);
  return cachedImg ? cachedImg.src : src;
};

// Force cache an image (useful for images that are already loaded in DOM)
export const cacheExistingImage = (src: string): void => {
  if (!imageCache.has(src)) {
    const img = new Image();
    img.src = src;
    if (img.complete) {
      imageCache.set(src, img);
      console.log(`🔄 Cached existing image: ${src}`);
    }
  }
};

// Clear our custom image cache (useful for development)
export const clearImageCache = (): void => {
  imageCache.clear();
  loadingPromises.clear();
  console.log('🗑️ Image cache cleared');

  // Also clear browser cache if available
  if ('caches' in window) {
    caches.keys().then((names) => {
      names.forEach((name) => {
        if (name.includes('image')) {
          caches.delete(name);
        }
      });
    });
  }
};

// Get cache statistics
export const getCacheStats = () => {
  return {
    cachedImages: imageCache.size,
    loadingImages: loadingPromises.size,
    cachedUrls: Array.from(imageCache.keys())
  };
};
