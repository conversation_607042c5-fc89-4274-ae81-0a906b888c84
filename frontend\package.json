{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@barba/core": "^2.10.3", "@barba/router": "^2.1.11", "@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^5.1.1", "@splinetool/react-spline": "^4.1.0", "@studio-freight/lenis": "^1.0.42", "@supabase/supabase-js": "^2.52.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^24.0.13", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "cobe": "^0.6.4", "framer-motion": "^12.23.6", "gl-matrix": "^3.4.3", "gsap": "^3.13.0", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "motion": "^12.23.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.4"}}