#!/usr/bin/env node

/**
 * <PERSON>ript to update domain references for SEO files
 * Usage: node scripts/update-domain.js your-domain.com
 */

const fs = require('fs');
const path = require('path');

const OLD_DOMAIN = 'deltaxerocreations.com';
const args = process.argv.slice(2);

if (args.length === 0) {
  console.error('❌ Please provide a domain name');
  console.log('Usage: node scripts/update-domain.js your-domain.com');
  process.exit(1);
}

const NEW_DOMAIN = args[0];

// Files to update
const filesToUpdate = [
  'public/sitemap.xml',
  'public/robots.txt',
  'src/components/SEOHead.tsx',
  'src/data/seoData.ts',
  '../index.html'
];

console.log(`🔄 Updating domain from ${OLD_DOMAIN} to ${NEW_DOMAIN}...`);

let updatedFiles = 0;

filesToUpdate.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  
  try {
    if (fs.existsSync(fullPath)) {
      let content = fs.readFileSync(fullPath, 'utf8');
      const originalContent = content;
      
      // Replace all instances of the old domain
      content = content.replace(new RegExp(`https://${OLD_DOMAIN}`, 'g'), `https://${NEW_DOMAIN}`);
      content = content.replace(new RegExp(OLD_DOMAIN, 'g'), NEW_DOMAIN);
      
      if (content !== originalContent) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Updated: ${filePath}`);
        updatedFiles++;
      } else {
        console.log(`⏭️  No changes needed: ${filePath}`);
      }
    } else {
      console.log(`⚠️  File not found: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
});

console.log(`\n🎉 Domain update complete! Updated ${updatedFiles} files.`);
console.log(`\n📋 Next steps:`);
console.log(`1. Update your Google Analytics tracking ID`);
console.log(`2. Create and add favicon files to public/`);
console.log(`3. Create Open Graph images`);
console.log(`4. Deploy your site`);
console.log(`5. Submit sitemap to Google Search Console`);
