import React, { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/ProtectedRoute'
import ModernLayout from './components/ModernLayout'
import AdminLayout from './components/AdminLayout'
import { LoadingScreen } from './components/LoadingScreen'
import { useAppLoading } from './hooks/useAppLoading'
import Home from './pages/Home'
import About from './pages/About'
import Services from './pages/Services'
import Jobs from './pages/Jobs'
import Blog from './pages/Blog'
import BlogPost from './pages/BlogPost'
import GetQuote from './pages/GetQuote'
import Contact from './pages/Contact'
import WebDevelopment from './pages/WebDevelopment'
import MobileDevelopment from './pages/MobileDevelopment'
import UIUXDesign from './pages/UIUXDesign'
import APIDevelopment from './pages/APIDevelopment'
import BrandStrategy from './pages/BrandStrategy'
import DigitalTransformation from './pages/DigitalTransformation'
import NavbarDemo from './pages/NavbarDemo'
import GooeyTextDemo from './pages/GooeyTextDemo'
import GooeyTextTest from './pages/GooeyTextTest'
import EcommercePage from './pages/EcommercePage'
import SaasPage from './pages/SaasPage'
import HealthcarePage from './pages/HealthcarePage'
import FintechPage from './pages/FintechPage'
import EducationPage from './pages/EducationPage'
import BankingPage from './pages/BankingPage'
import DigitalExperiencePlatforms from './pages/DigitalExperiencePlatforms'
import MobileEngineering from './pages/MobileEngineering'
import ExperienceDesign from './pages/ExperienceDesign'
import PerformanceOptimization from './pages/PerformanceOptimization'
import LegalTerms from './pages/LegalTerms'
import LegalPrivacy from './pages/LegalPrivacy'

// Admin Pages
import AdminLogin from './pages/admin/AdminLogin'
import ChangePassword from './pages/admin/ChangePassword'
import AdminDashboard from './pages/admin/AdminDashboard'
import ApplicationReview from './pages/admin/ApplicationReview'
import UserManagement from './pages/admin/UserManagement'
import ApplicationDetail from './pages/admin/ApplicationDetail'
import QuoteManagement from './pages/admin/QuoteManagement'

// Application Forms
import JobApplication from './pages/JobApplication'
import InternshipApplication from './pages/InternshipApplication'


const App = () => {
  const [showApp, setShowApp] = useState(false);
  const { isLoading, imageProgress, progress } = useAppLoading({
    simulateDbLoad: true,
    preloadImages: true,
    minLoadTime: 800,
    maxLoadTime: 2500
  });

  const handleLoadingComplete = () => {
    setShowApp(true);
  };

  return (
    <AuthProvider>
      <Router>
        <div className="text-white min-h-screen overflow-x-hidden max-w-full">
          {/* Loading Screen */}
          <LoadingScreen
            isLoading={isLoading}
            onLoadingComplete={handleLoadingComplete}
            texts={["Delta", "Xero", "Creations", "Welcome"]}
            morphTime={1.2}
            cooldownTime={0.3}
            minDisplayTime={5000}
            imageProgress={imageProgress}
            overallProgress={progress.overall}
          />

          {/* Main App Content */}
          {showApp && (
            <AnimatePresence mode="wait">
              <Routes>
              {/* Admin Authentication Routes */}
              <Route path="/admin/login" element={<AdminLogin />} />
              <Route path="/admin/change-password" element={
                <ProtectedRoute requireAdmin={true}>
                  <ChangePassword />
                </ProtectedRoute>
              } />

              {/* Protected Admin Routes */}
              <Route path="/admin" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <AdminDashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/dashboard" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <AdminDashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/applications" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <ApplicationReview />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/applications/:id" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <ApplicationDetail />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/quotes" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <QuoteManagement />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/users" element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout>
                    <UserManagement />
                  </AdminLayout>
                </ProtectedRoute>
              } />

            {/* Main website routes - With main navbar */}
            <Route path="/*" element={
              <ModernLayout>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/about" element={<About />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/jobs" element={<Jobs />} />
                  <Route path="/blog" element={<Blog />} />
                  <Route path="/blog/:slug" element={<BlogPost />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/quote" element={<GetQuote />} />
                  <Route path="/get-quote" element={<GetQuote />} />

                  {/* Service-specific pages */}
                  <Route path="/web-development" element={<WebDevelopment />} />
                  <Route path="/mobile-development" element={<MobileDevelopment />} />
                  <Route path="/ui-ux-design" element={<UIUXDesign />} />
                  <Route path="/api-development" element={<APIDevelopment />} />
                  <Route path="/brand-strategy" element={<BrandStrategy />} />
                  <Route path="/digital-transformation" element={<DigitalTransformation />} />

                  {/* Home page service pages */}
                  <Route path="/digital-experience-platforms" element={<DigitalExperiencePlatforms />} />
                  <Route path="/mobile-engineering" element={<MobileEngineering />} />
                  <Route path="/experience-design" element={<ExperienceDesign />} />
                  <Route path="/performance-optimization" element={<PerformanceOptimization />} />

                  {/* Application Forms */}
                  <Route path="/apply/job" element={<JobApplication />} />
                  <Route path="/apply/internship" element={<InternshipApplication />} />
                  <Route path="/careers/apply" element={<JobApplication />} />
                  <Route path="/internships/apply" element={<InternshipApplication />} />

                  {/* Industry-specific pages */}
                  <Route path="/ecommerce" element={<EcommercePage />} />
                  <Route path="/saas" element={<SaasPage />} />
                  <Route path="/healthcare" element={<HealthcarePage />} />
                  <Route path="/fintech" element={<FintechPage />} />
                  <Route path="/education" element={<EducationPage />} />
                  <Route path="/banking" element={<BankingPage />} />

                  {/* Legal pages */}
                  <Route path="/legal/terms" element={<LegalTerms />} />
                  <Route path="/legal/privacy" element={<LegalPrivacy />} />
                  <Route path="/company/terms" element={<LegalTerms />} />
                  <Route path="/company/privacy" element={<LegalPrivacy />} />
                  {/* Backup routes for SEO/bookmarks */}
                  <Route path="/terms-and-conditions" element={<LegalTerms />} />
                  <Route path="/privacy-policy" element={<LegalPrivacy />} />

                  {/* Demo pages */}
                  <Route path="/navbar-demo" element={<NavbarDemo />} />
                  <Route path="/gooey-text-demo" element={<GooeyTextDemo />} />
                  <Route path="/gooey-text-test" element={<GooeyTextTest />} />
                </Routes>
              </ModernLayout>
            } />
              </Routes>
            </AnimatePresence>
          )}
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App