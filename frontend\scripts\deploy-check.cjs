#!/usr/bin/env node

/**
 * Pre-deployment check script for SEO and production readiness
 * Usage: node scripts/deploy-check.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Running pre-deployment checks...\n');

let allChecks = true;

// Check 1: Verify sitemap.xml exists
const sitemapPath = path.join(__dirname, '..', 'public', 'sitemap.xml');
if (fs.existsSync(sitemapPath)) {
  console.log('✅ sitemap.xml found');
} else {
  console.log('❌ sitemap.xml missing');
  allChecks = false;
}

// Check 2: Verify robots.txt exists
const robotsPath = path.join(__dirname, '..', 'public', 'robots.txt');
if (fs.existsSync(robotsPath)) {
  console.log('✅ robots.txt found');
} else {
  console.log('❌ robots.txt missing');
  allChecks = false;
}

// Check 3: Verify site.webmanifest exists
const manifestPath = path.join(__dirname, '..', 'public', 'site.webmanifest');
if (fs.existsSync(manifestPath)) {
  console.log('✅ site.webmanifest found');
} else {
  console.log('❌ site.webmanifest missing');
  allChecks = false;
}

// Check 4: Verify SEO components exist
const seoHeadPath = path.join(__dirname, '..', 'src', 'components', 'SEOHead.tsx');
if (fs.existsSync(seoHeadPath)) {
  console.log('✅ SEOHead component found');
} else {
  console.log('❌ SEOHead component missing');
  allChecks = false;
}

// Check 5: Verify SEO data exists
const seoDataPath = path.join(__dirname, '..', 'src', 'data', 'seoData.ts');
if (fs.existsSync(seoDataPath)) {
  console.log('✅ SEO data configuration found');
} else {
  console.log('❌ SEO data configuration missing');
  allChecks = false;
}

// Check 6: Verify no react-helmet-async dependency
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (!packageJson.dependencies['react-helmet-async']) {
    console.log('✅ No conflicting react-helmet-async dependency');
  } else {
    console.log('❌ react-helmet-async dependency found (will cause build issues)');
    allChecks = false;
  }
} else {
  console.log('❌ package.json not found');
  allChecks = false;
}

// Check 7: Verify build directory exists
const buildPath = path.join(__dirname, '..', 'dist');
if (fs.existsSync(buildPath)) {
  console.log('✅ Build directory (dist) found');
} else {
  console.log('⚠️  Build directory (dist) not found - run "npm run build" first');
}

console.log('\n📋 Deployment Checklist:');
console.log('□ Update domain references (run: node scripts/update-domain.js yourdomain.com)');
console.log('□ Add favicon files to public/ directory');
console.log('□ Create Open Graph images');
console.log('□ Set up Google Analytics tracking ID');
console.log('□ Configure environment variables for production');
console.log('□ Test the build locally (npm run preview)');

if (allChecks) {
  console.log('\n🎉 All checks passed! Ready for deployment.');
  process.exit(0);
} else {
  console.log('\n❌ Some checks failed. Please fix the issues before deploying.');
  process.exit(1);
}
