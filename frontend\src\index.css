/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap'); */
@import './styles/performance.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-optimized marquee animation */
@keyframes marquee-mobile {
  0% { transform: translateX(0%); }
  100% { transform: translateX(-50%); }
}

@layer base {
  html {
    /* Disable default smooth scroll behavior to let Lenis handle it */
    scroll-behavior: auto;
  }

  html, body {
    margin: 0;
    padding: 0;
  }

  /* Lenis smooth scroll styles */
  html.lenis {
    height: auto;
  }

  .lenis.lenis-smooth {
    scroll-behavior: auto;
  }

  .lenis.lenis-smooth [data-lenis-prevent] {
    overscroll-behavior: contain;
  }

  .lenis.lenis-stopped {
    overflow: hidden;
  }

  .lenis.lenis-scrolling iframe {
    pointer-events: none;
  }

  /* Prevent initial layout shift and jitter */
  body {
    overflow-x: hidden;
    overscroll-behavior: none;
  }

  /* Optimize for smooth scrolling performance */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Optimize services section for smooth scrolling */
  .services-section {
    contain: layout style paint;
    will-change: auto;
  }

  /* Reduce motion blur and improve card performance */
  .gradient-card {
    contain: layout style;
    will-change: transform;
  }

  .gradient-card:not(:hover) {
    will-change: auto;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #020617;
    color: #ffffff;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
  }
}

@layer components {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.1);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

/* Loading animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Prose styles for blog content */
.prose-invert {
  color: #e5e7eb;
}

.prose-invert h2 {
  color: #ffffff;
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.prose-invert h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.prose-invert p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
  color: #d1d5db;
}

.prose-invert ul {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.prose-invert li {
  margin-bottom: 0.5rem;
  color: #d1d5db;
  line-height: 1.6;
}

.prose-invert strong {
  color: #ffffff;
  font-weight: 600;
}

.prose-invert code {
  background-color: #374151;
  color: #60a5fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.prose-lg {
  font-size: 1.125rem;
  line-height: 1.7;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern Card Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Scroll Performance Optimizations */
.scroll-smooth {
  scroll-behavior: smooth;
}

.gpu-accelerated {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  -webkit-perspective: 1000;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}