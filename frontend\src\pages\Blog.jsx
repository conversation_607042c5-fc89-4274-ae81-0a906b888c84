import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom'
import { blogPosts, getFeaturedPosts } from '../data/blogPosts';

// Blog Post Card Component
const BlogCard = ({ post }) => {
  return (
    <motion.div
      className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden h-full hover:shadow-lg hover:shadow-primary-500/10 hover:border-primary-500/30 transition-all duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <div className="relative w-full h-48 bg-gradient-to-br from-dark-800 to-dark-900 flex items-center justify-center">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-48 object-cover"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextElementSibling.style.display = 'flex';
            }}
          />
          <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-4">
            <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center mb-3">
              <span className="text-white font-bold text-sm">DX</span>
            </div>
            <div className="text-primary-400 font-semibold text-sm">Delta Xero Creations</div>
            <div className="text-gray-400 text-xs mt-1">Blog Article</div>
          </div>
        </div>
        <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
          {post.category}
        </div>
      </div>
      <div className="p-6">
        <div className="flex items-center text-sm text-gray-400 mb-3">
          <span>{post.date}</span>
          <span className="mx-2">•</span>
          <span>{post.readTime} min read</span>
        </div>
        <h3 className="text-xl font-bold text-white mb-3 hover:text-primary-400 transition-colors">
          {post.title}
        </h3>
        <p className="text-gray-300 mb-4 line-clamp-3">
          {post.excerpt}
        </p>
        <div className="flex items-center justify-end">
          {post.tags && (
            <div className="flex flex-wrap gap-1 mt-2">
              {post.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="text-xs text-primary-400 bg-primary-500/10 px-2 py-1 rounded-full">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Featured Post Component
const FeaturedPost = ({ post }) => {
  return (
    <motion.div
      className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden mb-12"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="md:flex">
        <div className="md:w-1/2 relative">
          <div className="relative w-full h-64 md:h-full bg-gradient-to-br from-dark-800 to-dark-900 flex items-center justify-center">
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-64 md:h-full object-cover"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextElementSibling.style.display = 'flex';
              }}
            />
            <div className="absolute inset-0 hidden flex-col items-center justify-center text-center p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center mb-4">
                <span className="text-white font-bold text-lg">DX</span>
              </div>
              <div className="text-primary-400 font-bold text-lg">Delta Xero Creations</div>
              <div className="text-gray-400 text-sm mt-2">Featured Article</div>
            </div>
          </div>
          <div className="absolute top-4 left-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-xs font-bold px-3 py-1 rounded-full">
            Featured
          </div>
          <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
            {post.category}
          </div>
        </div>
        <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-center">
          <div className="flex items-center text-sm text-gray-400 mb-3">
            <span>{post.date}</span>
            <span className="mx-2">•</span>
            <span>{post.readTime} min read</span>
          </div>
          <h3 className="text-2xl font-bold text-white mb-4 hover:text-primary-400 transition-colors">
            {post.title}
          </h3>
          <p className="text-gray-300 mb-6">
            {post.excerpt}
          </p>
          <div className="flex flex-wrap gap-2 mb-6">
            {post.tags && post.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="text-xs text-primary-400 bg-primary-500/10 px-2 py-1 rounded-full">
                {tag}
              </span>
            ))}
          </div>
          <div className="inline-flex items-center text-primary-400 hover:text-primary-300 transition-colors">
            Read Full Article
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const Blog = () => {
  const [activeCategory, setActiveCategory] = useState('All');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Blog categories
  const categories = ['All', 'Web Development', 'Design', 'Mobile Development', 'Performance', 'Business'];

  // Get featured post
  const featuredPost = featuredPosts[0];

  // Get regular posts filtered by category
  const filteredPosts = blogPosts.filter(post =>
    !post.featured && (activeCategory === 'All' || post.category === activeCategory)
  );

  return (
    <section className="py-32 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Delta Xero <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-400 to-primary-600">Blog</span>
            </h1>
            <p className="text-xl text-gray-300">
              Insights, tips, and stories about web development, design, and digital innovation.
            </p>
          </motion.div>

          {/* Categories */}
          <motion.div
            className="flex flex-wrap gap-2 justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white'
                    : 'bg-dark-800/50 text-gray-300 hover:bg-dark-700/50'
                }`}
                onClick={() => setActiveCategory(category)}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* Featured Post */}
          {featuredPost && activeCategory === 'All' && (
            <Link to={`/blog/${featuredPost.slug}`}>
              <FeaturedPost post={featuredPost} />
            </Link>
          )}

          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map(post => (
              <Link key={post.id} to={`/blog/${post.slug}`}>
                <BlogCard post={post} />
              </Link>
            ))}
          </div>

          {/* Newsletter Signup */}
          <motion.div
            className="mt-20 text-center bg-dark-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Stay <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Updated</span>
            </h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Subscribe to our newsletter and get the latest insights on web development, design trends, and business growth delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary-500 focus:outline-none"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
              >
                Subscribe
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Blog
