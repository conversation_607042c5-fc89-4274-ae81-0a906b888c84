import { useLocation } from 'react-router-dom'
import { seoData, SEOData } from '../data/seoData'

export const useSEO = (customSEO?: Partial<SEOData>): SEOData => {
  const location = useLocation()
  
  // Get the current path and clean it up
  const currentPath = location.pathname.replace(/^\//, '').replace(/\/$/, '') || 'home'
  
  // Get the default SEO data for the current page
  const defaultSEO = seoData[currentPath] || seoData.home
  
  // Merge with custom SEO data if provided
  return {
    ...defaultSEO,
    ...customSEO
  }
}

export default useSEO
