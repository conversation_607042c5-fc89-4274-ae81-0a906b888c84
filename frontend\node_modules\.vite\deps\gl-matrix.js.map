{"version": 3, "sources": ["../../gl-matrix/esm/common.js", "../../gl-matrix/esm/mat2.js", "../../gl-matrix/esm/mat2d.js", "../../gl-matrix/esm/mat3.js", "../../gl-matrix/esm/mat4.js", "../../gl-matrix/esm/quat.js", "../../gl-matrix/esm/vec3.js", "../../gl-matrix/esm/vec4.js", "../../gl-matrix/esm/quat2.js", "../../gl-matrix/esm/vec2.js"], "sourcesContent": ["/**\n * Common utilities\n * @module glMatrix\n */\n// Configuration Constants\nexport var EPSILON = 0.000001;\nexport var ARRAY_TYPE = typeof Float32Array !== 'undefined' ? Float32Array : Array;\nexport var RANDOM = Math.random;\n/**\n * Sets the type of array used when creating new vectors and matrices\n *\n * @param {Float32ArrayConstructor | ArrayConstructor} type Array type, such as Float32Array or Array\n */\n\nexport function setMatrixArrayType(type) {\n  ARRAY_TYPE = type;\n}\nvar degree = Math.PI / 180;\n/**\n * Convert Degree To Radian\n *\n * @param {Number} a Angle in Degrees\n */\n\nexport function toRadian(a) {\n  return a * degree;\n}\n/**\n * Tests whether or not the arguments have approximately the same value, within an absolute\n * or relative tolerance of glMatrix.EPSILON (an absolute tolerance is used for values less\n * than or equal to 1.0, and a relative tolerance is used for larger values)\n *\n * @param {Number} a The first number to test.\n * @param {Number} b The second number to test.\n * @returns {Boolean} True if the numbers are approximately equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  return Math.abs(a - b) <= EPSILON * Math.max(1.0, Math.abs(a), Math.abs(b));\n}\nif (!Math.hypot) Math.hypot = function () {\n  var y = 0,\n      i = arguments.length;\n\n  while (i--) {\n    y += arguments[i] * arguments[i];\n  }\n\n  return Math.sqrt(y);\n};", "import * as glMatrix from \"./common.js\";\n/**\n * 2x2 Matrix\n * @module mat2\n */\n\n/**\n * Creates a new identity mat2\n *\n * @returns {mat2} a new 2x2 matrix\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(4);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[1] = 0;\n    out[2] = 0;\n  }\n\n  out[0] = 1;\n  out[3] = 1;\n  return out;\n}\n/**\n * Creates a new mat2 initialized with values from an existing matrix\n *\n * @param {ReadonlyMat2} a matrix to clone\n * @returns {mat2} a new 2x2 matrix\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(4);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\n/**\n * Copy the values from one mat2 to another\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the source matrix\n * @returns {mat2} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\n/**\n * Set a mat2 to the identity matrix\n *\n * @param {mat2} out the receiving matrix\n * @returns {mat2} out\n */\n\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  return out;\n}\n/**\n * Create a new mat2 with the given values\n *\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m10 Component in column 1, row 0 position (index 2)\n * @param {Number} m11 Component in column 1, row 1 position (index 3)\n * @returns {mat2} out A new 2x2 matrix\n */\n\nexport function fromValues(m00, m01, m10, m11) {\n  var out = new glMatrix.ARRAY_TYPE(4);\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m10;\n  out[3] = m11;\n  return out;\n}\n/**\n * Set the components of a mat2 to the given values\n *\n * @param {mat2} out the receiving matrix\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m10 Component in column 1, row 0 position (index 2)\n * @param {Number} m11 Component in column 1, row 1 position (index 3)\n * @returns {mat2} out\n */\n\nexport function set(out, m00, m01, m10, m11) {\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m10;\n  out[3] = m11;\n  return out;\n}\n/**\n * Transpose the values of a mat2\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the source matrix\n * @returns {mat2} out\n */\n\nexport function transpose(out, a) {\n  // If we are transposing ourselves we can skip a few steps but have to cache\n  // some values\n  if (out === a) {\n    var a1 = a[1];\n    out[1] = a[2];\n    out[2] = a1;\n  } else {\n    out[0] = a[0];\n    out[1] = a[2];\n    out[2] = a[1];\n    out[3] = a[3];\n  }\n\n  return out;\n}\n/**\n * Inverts a mat2\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the source matrix\n * @returns {mat2} out\n */\n\nexport function invert(out, a) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3]; // Calculate the determinant\n\n  var det = a0 * a3 - a2 * a1;\n\n  if (!det) {\n    return null;\n  }\n\n  det = 1.0 / det;\n  out[0] = a3 * det;\n  out[1] = -a1 * det;\n  out[2] = -a2 * det;\n  out[3] = a0 * det;\n  return out;\n}\n/**\n * Calculates the adjugate of a mat2\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the source matrix\n * @returns {mat2} out\n */\n\nexport function adjoint(out, a) {\n  // Caching this value is nessecary if out == a\n  var a0 = a[0];\n  out[0] = a[3];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  out[3] = a0;\n  return out;\n}\n/**\n * Calculates the determinant of a mat2\n *\n * @param {ReadonlyMat2} a the source matrix\n * @returns {Number} determinant of a\n */\n\nexport function determinant(a) {\n  return a[0] * a[3] - a[2] * a[1];\n}\n/**\n * Multiplies two mat2's\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the first operand\n * @param {ReadonlyMat2} b the second operand\n * @returns {mat2} out\n */\n\nexport function multiply(out, a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3];\n  out[0] = a0 * b0 + a2 * b1;\n  out[1] = a1 * b0 + a3 * b1;\n  out[2] = a0 * b2 + a2 * b3;\n  out[3] = a1 * b2 + a3 * b3;\n  return out;\n}\n/**\n * Rotates a mat2 by the given angle\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat2} out\n */\n\nexport function rotate(out, a, rad) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  out[0] = a0 * c + a2 * s;\n  out[1] = a1 * c + a3 * s;\n  out[2] = a0 * -s + a2 * c;\n  out[3] = a1 * -s + a3 * c;\n  return out;\n}\n/**\n * Scales the mat2 by the dimensions in the given vec2\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the matrix to rotate\n * @param {ReadonlyVec2} v the vec2 to scale the matrix by\n * @returns {mat2} out\n **/\n\nexport function scale(out, a, v) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var v0 = v[0],\n      v1 = v[1];\n  out[0] = a0 * v0;\n  out[1] = a1 * v0;\n  out[2] = a2 * v1;\n  out[3] = a3 * v1;\n  return out;\n}\n/**\n * Creates a matrix from a given angle\n * This is equivalent to (but much faster than):\n *\n *     mat2.identity(dest);\n *     mat2.rotate(dest, dest, rad);\n *\n * @param {mat2} out mat2 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat2} out\n */\n\nexport function fromRotation(out, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  out[0] = c;\n  out[1] = s;\n  out[2] = -s;\n  out[3] = c;\n  return out;\n}\n/**\n * Creates a matrix from a vector scaling\n * This is equivalent to (but much faster than):\n *\n *     mat2.identity(dest);\n *     mat2.scale(dest, dest, vec);\n *\n * @param {mat2} out mat2 receiving operation result\n * @param {ReadonlyVec2} v Scaling vector\n * @returns {mat2} out\n */\n\nexport function fromScaling(out, v) {\n  out[0] = v[0];\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = v[1];\n  return out;\n}\n/**\n * Returns a string representation of a mat2\n *\n * @param {ReadonlyMat2} a matrix to represent as a string\n * @returns {String} string representation of the matrix\n */\n\nexport function str(a) {\n  return \"mat2(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \")\";\n}\n/**\n * Returns Frobenius norm of a mat2\n *\n * @param {ReadonlyMat2} a the matrix to calculate Frobenius norm of\n * @returns {Number} Frobenius norm\n */\n\nexport function frob(a) {\n  return Math.hypot(a[0], a[1], a[2], a[3]);\n}\n/**\n * Returns L, D and U matrices (Lower triangular, Diagonal and Upper triangular) by factorizing the input matrix\n * @param {ReadonlyMat2} L the lower triangular matrix\n * @param {ReadonlyMat2} D the diagonal matrix\n * @param {ReadonlyMat2} U the upper triangular matrix\n * @param {ReadonlyMat2} a the input matrix to factorize\n */\n\nexport function LDU(L, D, U, a) {\n  L[2] = a[2] / a[0];\n  U[0] = a[0];\n  U[1] = a[1];\n  U[3] = a[3] - L[2] * U[1];\n  return [L, D, U];\n}\n/**\n * Adds two mat2's\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the first operand\n * @param {ReadonlyMat2} b the second operand\n * @returns {mat2} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  return out;\n}\n/**\n * Subtracts matrix b from matrix a\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the first operand\n * @param {ReadonlyMat2} b the second operand\n * @returns {mat2} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  out[3] = a[3] - b[3];\n  return out;\n}\n/**\n * Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyMat2} a The first matrix.\n * @param {ReadonlyMat2} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3];\n}\n/**\n * Returns whether or not the matrices have approximately the same elements in the same position.\n *\n * @param {ReadonlyMat2} a The first matrix.\n * @param {ReadonlyMat2} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3));\n}\n/**\n * Multiply each element of the matrix by a scalar.\n *\n * @param {mat2} out the receiving matrix\n * @param {ReadonlyMat2} a the matrix to scale\n * @param {Number} b amount to scale the matrix's elements by\n * @returns {mat2} out\n */\n\nexport function multiplyScalar(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  return out;\n}\n/**\n * Adds two mat2's after multiplying each element of the second operand by a scalar value.\n *\n * @param {mat2} out the receiving vector\n * @param {ReadonlyMat2} a the first operand\n * @param {ReadonlyMat2} b the second operand\n * @param {Number} scale the amount to scale b's elements by before adding\n * @returns {mat2} out\n */\n\nexport function multiplyScalarAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  out[3] = a[3] + b[3] * scale;\n  return out;\n}\n/**\n * Alias for {@link mat2.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link mat2.subtract}\n * @function\n */\n\nexport var sub = subtract;", "import * as glMatrix from \"./common.js\";\n/**\n * 2x3 Matrix\n * @module mat2d\n * @description\n * A mat2d contains six elements defined as:\n * <pre>\n * [a, b,\n *  c, d,\n *  tx, ty]\n * </pre>\n * This is a short form for the 3x3 matrix:\n * <pre>\n * [a, b, 0,\n *  c, d, 0,\n *  tx, ty, 1]\n * </pre>\n * The last column is ignored so the array is shorter and operations are faster.\n */\n\n/**\n * Creates a new identity mat2d\n *\n * @returns {mat2d} a new 2x3 matrix\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(6);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[1] = 0;\n    out[2] = 0;\n    out[4] = 0;\n    out[5] = 0;\n  }\n\n  out[0] = 1;\n  out[3] = 1;\n  return out;\n}\n/**\n * Creates a new mat2d initialized with values from an existing matrix\n *\n * @param {ReadonlyMat2d} a matrix to clone\n * @returns {mat2d} a new 2x3 matrix\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(6);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  return out;\n}\n/**\n * Copy the values from one mat2d to another\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the source matrix\n * @returns {mat2d} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  return out;\n}\n/**\n * Set a mat2d to the identity matrix\n *\n * @param {mat2d} out the receiving matrix\n * @returns {mat2d} out\n */\n\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = 0;\n  out[5] = 0;\n  return out;\n}\n/**\n * Create a new mat2d with the given values\n *\n * @param {Number} a Component A (index 0)\n * @param {Number} b Component B (index 1)\n * @param {Number} c Component C (index 2)\n * @param {Number} d Component D (index 3)\n * @param {Number} tx Component TX (index 4)\n * @param {Number} ty Component TY (index 5)\n * @returns {mat2d} A new mat2d\n */\n\nexport function fromValues(a, b, c, d, tx, ty) {\n  var out = new glMatrix.ARRAY_TYPE(6);\n  out[0] = a;\n  out[1] = b;\n  out[2] = c;\n  out[3] = d;\n  out[4] = tx;\n  out[5] = ty;\n  return out;\n}\n/**\n * Set the components of a mat2d to the given values\n *\n * @param {mat2d} out the receiving matrix\n * @param {Number} a Component A (index 0)\n * @param {Number} b Component B (index 1)\n * @param {Number} c Component C (index 2)\n * @param {Number} d Component D (index 3)\n * @param {Number} tx Component TX (index 4)\n * @param {Number} ty Component TY (index 5)\n * @returns {mat2d} out\n */\n\nexport function set(out, a, b, c, d, tx, ty) {\n  out[0] = a;\n  out[1] = b;\n  out[2] = c;\n  out[3] = d;\n  out[4] = tx;\n  out[5] = ty;\n  return out;\n}\n/**\n * Inverts a mat2d\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the source matrix\n * @returns {mat2d} out\n */\n\nexport function invert(out, a) {\n  var aa = a[0],\n      ab = a[1],\n      ac = a[2],\n      ad = a[3];\n  var atx = a[4],\n      aty = a[5];\n  var det = aa * ad - ab * ac;\n\n  if (!det) {\n    return null;\n  }\n\n  det = 1.0 / det;\n  out[0] = ad * det;\n  out[1] = -ab * det;\n  out[2] = -ac * det;\n  out[3] = aa * det;\n  out[4] = (ac * aty - ad * atx) * det;\n  out[5] = (ab * atx - aa * aty) * det;\n  return out;\n}\n/**\n * Calculates the determinant of a mat2d\n *\n * @param {ReadonlyMat2d} a the source matrix\n * @returns {Number} determinant of a\n */\n\nexport function determinant(a) {\n  return a[0] * a[3] - a[1] * a[2];\n}\n/**\n * Multiplies two mat2d's\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the first operand\n * @param {ReadonlyMat2d} b the second operand\n * @returns {mat2d} out\n */\n\nexport function multiply(out, a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3],\n      b4 = b[4],\n      b5 = b[5];\n  out[0] = a0 * b0 + a2 * b1;\n  out[1] = a1 * b0 + a3 * b1;\n  out[2] = a0 * b2 + a2 * b3;\n  out[3] = a1 * b2 + a3 * b3;\n  out[4] = a0 * b4 + a2 * b5 + a4;\n  out[5] = a1 * b4 + a3 * b5 + a5;\n  return out;\n}\n/**\n * Rotates a mat2d by the given angle\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat2d} out\n */\n\nexport function rotate(out, a, rad) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5];\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  out[0] = a0 * c + a2 * s;\n  out[1] = a1 * c + a3 * s;\n  out[2] = a0 * -s + a2 * c;\n  out[3] = a1 * -s + a3 * c;\n  out[4] = a4;\n  out[5] = a5;\n  return out;\n}\n/**\n * Scales the mat2d by the dimensions in the given vec2\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the matrix to translate\n * @param {ReadonlyVec2} v the vec2 to scale the matrix by\n * @returns {mat2d} out\n **/\n\nexport function scale(out, a, v) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5];\n  var v0 = v[0],\n      v1 = v[1];\n  out[0] = a0 * v0;\n  out[1] = a1 * v0;\n  out[2] = a2 * v1;\n  out[3] = a3 * v1;\n  out[4] = a4;\n  out[5] = a5;\n  return out;\n}\n/**\n * Translates the mat2d by the dimensions in the given vec2\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the matrix to translate\n * @param {ReadonlyVec2} v the vec2 to translate the matrix by\n * @returns {mat2d} out\n **/\n\nexport function translate(out, a, v) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5];\n  var v0 = v[0],\n      v1 = v[1];\n  out[0] = a0;\n  out[1] = a1;\n  out[2] = a2;\n  out[3] = a3;\n  out[4] = a0 * v0 + a2 * v1 + a4;\n  out[5] = a1 * v0 + a3 * v1 + a5;\n  return out;\n}\n/**\n * Creates a matrix from a given angle\n * This is equivalent to (but much faster than):\n *\n *     mat2d.identity(dest);\n *     mat2d.rotate(dest, dest, rad);\n *\n * @param {mat2d} out mat2d receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat2d} out\n */\n\nexport function fromRotation(out, rad) {\n  var s = Math.sin(rad),\n      c = Math.cos(rad);\n  out[0] = c;\n  out[1] = s;\n  out[2] = -s;\n  out[3] = c;\n  out[4] = 0;\n  out[5] = 0;\n  return out;\n}\n/**\n * Creates a matrix from a vector scaling\n * This is equivalent to (but much faster than):\n *\n *     mat2d.identity(dest);\n *     mat2d.scale(dest, dest, vec);\n *\n * @param {mat2d} out mat2d receiving operation result\n * @param {ReadonlyVec2} v Scaling vector\n * @returns {mat2d} out\n */\n\nexport function fromScaling(out, v) {\n  out[0] = v[0];\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = v[1];\n  out[4] = 0;\n  out[5] = 0;\n  return out;\n}\n/**\n * Creates a matrix from a vector translation\n * This is equivalent to (but much faster than):\n *\n *     mat2d.identity(dest);\n *     mat2d.translate(dest, dest, vec);\n *\n * @param {mat2d} out mat2d receiving operation result\n * @param {ReadonlyVec2} v Translation vector\n * @returns {mat2d} out\n */\n\nexport function fromTranslation(out, v) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = v[0];\n  out[5] = v[1];\n  return out;\n}\n/**\n * Returns a string representation of a mat2d\n *\n * @param {ReadonlyMat2d} a matrix to represent as a string\n * @returns {String} string representation of the matrix\n */\n\nexport function str(a) {\n  return \"mat2d(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \", \" + a[4] + \", \" + a[5] + \")\";\n}\n/**\n * Returns Frobenius norm of a mat2d\n *\n * @param {ReadonlyMat2d} a the matrix to calculate Frobenius norm of\n * @returns {Number} Frobenius norm\n */\n\nexport function frob(a) {\n  return Math.hypot(a[0], a[1], a[2], a[3], a[4], a[5], 1);\n}\n/**\n * Adds two mat2d's\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the first operand\n * @param {ReadonlyMat2d} b the second operand\n * @returns {mat2d} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  out[4] = a[4] + b[4];\n  out[5] = a[5] + b[5];\n  return out;\n}\n/**\n * Subtracts matrix b from matrix a\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the first operand\n * @param {ReadonlyMat2d} b the second operand\n * @returns {mat2d} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  out[3] = a[3] - b[3];\n  out[4] = a[4] - b[4];\n  out[5] = a[5] - b[5];\n  return out;\n}\n/**\n * Multiply each element of the matrix by a scalar.\n *\n * @param {mat2d} out the receiving matrix\n * @param {ReadonlyMat2d} a the matrix to scale\n * @param {Number} b amount to scale the matrix's elements by\n * @returns {mat2d} out\n */\n\nexport function multiplyScalar(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  out[4] = a[4] * b;\n  out[5] = a[5] * b;\n  return out;\n}\n/**\n * Adds two mat2d's after multiplying each element of the second operand by a scalar value.\n *\n * @param {mat2d} out the receiving vector\n * @param {ReadonlyMat2d} a the first operand\n * @param {ReadonlyMat2d} b the second operand\n * @param {Number} scale the amount to scale b's elements by before adding\n * @returns {mat2d} out\n */\n\nexport function multiplyScalarAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  out[3] = a[3] + b[3] * scale;\n  out[4] = a[4] + b[4] * scale;\n  out[5] = a[5] + b[5] * scale;\n  return out;\n}\n/**\n * Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyMat2d} a The first matrix.\n * @param {ReadonlyMat2d} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3] && a[4] === b[4] && a[5] === b[5];\n}\n/**\n * Returns whether or not the matrices have approximately the same elements in the same position.\n *\n * @param {ReadonlyMat2d} a The first matrix.\n * @param {ReadonlyMat2d} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3],\n      b4 = b[4],\n      b5 = b[5];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3)) && Math.abs(a4 - b4) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a4), Math.abs(b4)) && Math.abs(a5 - b5) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a5), Math.abs(b5));\n}\n/**\n * Alias for {@link mat2d.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link mat2d.subtract}\n * @function\n */\n\nexport var sub = subtract;", "import * as glMatrix from \"./common.js\";\n/**\n * 3x3 Matrix\n * @module mat3\n */\n\n/**\n * Creates a new identity mat3\n *\n * @returns {mat3} a new 3x3 matrix\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(9);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[1] = 0;\n    out[2] = 0;\n    out[3] = 0;\n    out[5] = 0;\n    out[6] = 0;\n    out[7] = 0;\n  }\n\n  out[0] = 1;\n  out[4] = 1;\n  out[8] = 1;\n  return out;\n}\n/**\n * Copies the upper-left 3x3 values into the given mat3.\n *\n * @param {mat3} out the receiving 3x3 matrix\n * @param {ReadonlyMat4} a   the source 4x4 matrix\n * @returns {mat3} out\n */\n\nexport function fromMat4(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[4];\n  out[4] = a[5];\n  out[5] = a[6];\n  out[6] = a[8];\n  out[7] = a[9];\n  out[8] = a[10];\n  return out;\n}\n/**\n * Creates a new mat3 initialized with values from an existing matrix\n *\n * @param {ReadonlyMat3} a matrix to clone\n * @returns {mat3} a new 3x3 matrix\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(9);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  out[8] = a[8];\n  return out;\n}\n/**\n * Copy the values from one mat3 to another\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the source matrix\n * @returns {mat3} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  out[8] = a[8];\n  return out;\n}\n/**\n * Create a new mat3 with the given values\n *\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m02 Component in column 0, row 2 position (index 2)\n * @param {Number} m10 Component in column 1, row 0 position (index 3)\n * @param {Number} m11 Component in column 1, row 1 position (index 4)\n * @param {Number} m12 Component in column 1, row 2 position (index 5)\n * @param {Number} m20 Component in column 2, row 0 position (index 6)\n * @param {Number} m21 Component in column 2, row 1 position (index 7)\n * @param {Number} m22 Component in column 2, row 2 position (index 8)\n * @returns {mat3} A new mat3\n */\n\nexport function fromValues(m00, m01, m02, m10, m11, m12, m20, m21, m22) {\n  var out = new glMatrix.ARRAY_TYPE(9);\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m02;\n  out[3] = m10;\n  out[4] = m11;\n  out[5] = m12;\n  out[6] = m20;\n  out[7] = m21;\n  out[8] = m22;\n  return out;\n}\n/**\n * Set the components of a mat3 to the given values\n *\n * @param {mat3} out the receiving matrix\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m02 Component in column 0, row 2 position (index 2)\n * @param {Number} m10 Component in column 1, row 0 position (index 3)\n * @param {Number} m11 Component in column 1, row 1 position (index 4)\n * @param {Number} m12 Component in column 1, row 2 position (index 5)\n * @param {Number} m20 Component in column 2, row 0 position (index 6)\n * @param {Number} m21 Component in column 2, row 1 position (index 7)\n * @param {Number} m22 Component in column 2, row 2 position (index 8)\n * @returns {mat3} out\n */\n\nexport function set(out, m00, m01, m02, m10, m11, m12, m20, m21, m22) {\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m02;\n  out[3] = m10;\n  out[4] = m11;\n  out[5] = m12;\n  out[6] = m20;\n  out[7] = m21;\n  out[8] = m22;\n  return out;\n}\n/**\n * Set a mat3 to the identity matrix\n *\n * @param {mat3} out the receiving matrix\n * @returns {mat3} out\n */\n\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 1;\n  out[5] = 0;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 1;\n  return out;\n}\n/**\n * Transpose the values of a mat3\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the source matrix\n * @returns {mat3} out\n */\n\nexport function transpose(out, a) {\n  // If we are transposing ourselves we can skip a few steps but have to cache some values\n  if (out === a) {\n    var a01 = a[1],\n        a02 = a[2],\n        a12 = a[5];\n    out[1] = a[3];\n    out[2] = a[6];\n    out[3] = a01;\n    out[5] = a[7];\n    out[6] = a02;\n    out[7] = a12;\n  } else {\n    out[0] = a[0];\n    out[1] = a[3];\n    out[2] = a[6];\n    out[3] = a[1];\n    out[4] = a[4];\n    out[5] = a[7];\n    out[6] = a[2];\n    out[7] = a[5];\n    out[8] = a[8];\n  }\n\n  return out;\n}\n/**\n * Inverts a mat3\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the source matrix\n * @returns {mat3} out\n */\n\nexport function invert(out, a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2];\n  var a10 = a[3],\n      a11 = a[4],\n      a12 = a[5];\n  var a20 = a[6],\n      a21 = a[7],\n      a22 = a[8];\n  var b01 = a22 * a11 - a12 * a21;\n  var b11 = -a22 * a10 + a12 * a20;\n  var b21 = a21 * a10 - a11 * a20; // Calculate the determinant\n\n  var det = a00 * b01 + a01 * b11 + a02 * b21;\n\n  if (!det) {\n    return null;\n  }\n\n  det = 1.0 / det;\n  out[0] = b01 * det;\n  out[1] = (-a22 * a01 + a02 * a21) * det;\n  out[2] = (a12 * a01 - a02 * a11) * det;\n  out[3] = b11 * det;\n  out[4] = (a22 * a00 - a02 * a20) * det;\n  out[5] = (-a12 * a00 + a02 * a10) * det;\n  out[6] = b21 * det;\n  out[7] = (-a21 * a00 + a01 * a20) * det;\n  out[8] = (a11 * a00 - a01 * a10) * det;\n  return out;\n}\n/**\n * Calculates the adjugate of a mat3\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the source matrix\n * @returns {mat3} out\n */\n\nexport function adjoint(out, a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2];\n  var a10 = a[3],\n      a11 = a[4],\n      a12 = a[5];\n  var a20 = a[6],\n      a21 = a[7],\n      a22 = a[8];\n  out[0] = a11 * a22 - a12 * a21;\n  out[1] = a02 * a21 - a01 * a22;\n  out[2] = a01 * a12 - a02 * a11;\n  out[3] = a12 * a20 - a10 * a22;\n  out[4] = a00 * a22 - a02 * a20;\n  out[5] = a02 * a10 - a00 * a12;\n  out[6] = a10 * a21 - a11 * a20;\n  out[7] = a01 * a20 - a00 * a21;\n  out[8] = a00 * a11 - a01 * a10;\n  return out;\n}\n/**\n * Calculates the determinant of a mat3\n *\n * @param {ReadonlyMat3} a the source matrix\n * @returns {Number} determinant of a\n */\n\nexport function determinant(a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2];\n  var a10 = a[3],\n      a11 = a[4],\n      a12 = a[5];\n  var a20 = a[6],\n      a21 = a[7],\n      a22 = a[8];\n  return a00 * (a22 * a11 - a12 * a21) + a01 * (-a22 * a10 + a12 * a20) + a02 * (a21 * a10 - a11 * a20);\n}\n/**\n * Multiplies two mat3's\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the first operand\n * @param {ReadonlyMat3} b the second operand\n * @returns {mat3} out\n */\n\nexport function multiply(out, a, b) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2];\n  var a10 = a[3],\n      a11 = a[4],\n      a12 = a[5];\n  var a20 = a[6],\n      a21 = a[7],\n      a22 = a[8];\n  var b00 = b[0],\n      b01 = b[1],\n      b02 = b[2];\n  var b10 = b[3],\n      b11 = b[4],\n      b12 = b[5];\n  var b20 = b[6],\n      b21 = b[7],\n      b22 = b[8];\n  out[0] = b00 * a00 + b01 * a10 + b02 * a20;\n  out[1] = b00 * a01 + b01 * a11 + b02 * a21;\n  out[2] = b00 * a02 + b01 * a12 + b02 * a22;\n  out[3] = b10 * a00 + b11 * a10 + b12 * a20;\n  out[4] = b10 * a01 + b11 * a11 + b12 * a21;\n  out[5] = b10 * a02 + b11 * a12 + b12 * a22;\n  out[6] = b20 * a00 + b21 * a10 + b22 * a20;\n  out[7] = b20 * a01 + b21 * a11 + b22 * a21;\n  out[8] = b20 * a02 + b21 * a12 + b22 * a22;\n  return out;\n}\n/**\n * Translate a mat3 by the given vector\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the matrix to translate\n * @param {ReadonlyVec2} v vector to translate by\n * @returns {mat3} out\n */\n\nexport function translate(out, a, v) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a10 = a[3],\n      a11 = a[4],\n      a12 = a[5],\n      a20 = a[6],\n      a21 = a[7],\n      a22 = a[8],\n      x = v[0],\n      y = v[1];\n  out[0] = a00;\n  out[1] = a01;\n  out[2] = a02;\n  out[3] = a10;\n  out[4] = a11;\n  out[5] = a12;\n  out[6] = x * a00 + y * a10 + a20;\n  out[7] = x * a01 + y * a11 + a21;\n  out[8] = x * a02 + y * a12 + a22;\n  return out;\n}\n/**\n * Rotates a mat3 by the given angle\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat3} out\n */\n\nexport function rotate(out, a, rad) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a10 = a[3],\n      a11 = a[4],\n      a12 = a[5],\n      a20 = a[6],\n      a21 = a[7],\n      a22 = a[8],\n      s = Math.sin(rad),\n      c = Math.cos(rad);\n  out[0] = c * a00 + s * a10;\n  out[1] = c * a01 + s * a11;\n  out[2] = c * a02 + s * a12;\n  out[3] = c * a10 - s * a00;\n  out[4] = c * a11 - s * a01;\n  out[5] = c * a12 - s * a02;\n  out[6] = a20;\n  out[7] = a21;\n  out[8] = a22;\n  return out;\n}\n/**\n * Scales the mat3 by the dimensions in the given vec2\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the matrix to rotate\n * @param {ReadonlyVec2} v the vec2 to scale the matrix by\n * @returns {mat3} out\n **/\n\nexport function scale(out, a, v) {\n  var x = v[0],\n      y = v[1];\n  out[0] = x * a[0];\n  out[1] = x * a[1];\n  out[2] = x * a[2];\n  out[3] = y * a[3];\n  out[4] = y * a[4];\n  out[5] = y * a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  out[8] = a[8];\n  return out;\n}\n/**\n * Creates a matrix from a vector translation\n * This is equivalent to (but much faster than):\n *\n *     mat3.identity(dest);\n *     mat3.translate(dest, dest, vec);\n *\n * @param {mat3} out mat3 receiving operation result\n * @param {ReadonlyVec2} v Translation vector\n * @returns {mat3} out\n */\n\nexport function fromTranslation(out, v) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 1;\n  out[5] = 0;\n  out[6] = v[0];\n  out[7] = v[1];\n  out[8] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a given angle\n * This is equivalent to (but much faster than):\n *\n *     mat3.identity(dest);\n *     mat3.rotate(dest, dest, rad);\n *\n * @param {mat3} out mat3 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat3} out\n */\n\nexport function fromRotation(out, rad) {\n  var s = Math.sin(rad),\n      c = Math.cos(rad);\n  out[0] = c;\n  out[1] = s;\n  out[2] = 0;\n  out[3] = -s;\n  out[4] = c;\n  out[5] = 0;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a vector scaling\n * This is equivalent to (but much faster than):\n *\n *     mat3.identity(dest);\n *     mat3.scale(dest, dest, vec);\n *\n * @param {mat3} out mat3 receiving operation result\n * @param {ReadonlyVec2} v Scaling vector\n * @returns {mat3} out\n */\n\nexport function fromScaling(out, v) {\n  out[0] = v[0];\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = v[1];\n  out[5] = 0;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 1;\n  return out;\n}\n/**\n * Copies the values from a mat2d into a mat3\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat2d} a the matrix to copy\n * @returns {mat3} out\n **/\n\nexport function fromMat2d(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = 0;\n  out[3] = a[2];\n  out[4] = a[3];\n  out[5] = 0;\n  out[6] = a[4];\n  out[7] = a[5];\n  out[8] = 1;\n  return out;\n}\n/**\n * Calculates a 3x3 matrix from the given quaternion\n *\n * @param {mat3} out mat3 receiving operation result\n * @param {ReadonlyQuat} q Quaternion to create matrix from\n *\n * @returns {mat3} out\n */\n\nexport function fromQuat(out, q) {\n  var x = q[0],\n      y = q[1],\n      z = q[2],\n      w = q[3];\n  var x2 = x + x;\n  var y2 = y + y;\n  var z2 = z + z;\n  var xx = x * x2;\n  var yx = y * x2;\n  var yy = y * y2;\n  var zx = z * x2;\n  var zy = z * y2;\n  var zz = z * z2;\n  var wx = w * x2;\n  var wy = w * y2;\n  var wz = w * z2;\n  out[0] = 1 - yy - zz;\n  out[3] = yx - wz;\n  out[6] = zx + wy;\n  out[1] = yx + wz;\n  out[4] = 1 - xx - zz;\n  out[7] = zy - wx;\n  out[2] = zx - wy;\n  out[5] = zy + wx;\n  out[8] = 1 - xx - yy;\n  return out;\n}\n/**\n * Calculates a 3x3 normal matrix (transpose inverse) from the 4x4 matrix\n *\n * @param {mat3} out mat3 receiving operation result\n * @param {ReadonlyMat4} a Mat4 to derive the normal matrix from\n *\n * @returns {mat3} out\n */\n\nexport function normalFromMat4(out, a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a03 = a[3];\n  var a10 = a[4],\n      a11 = a[5],\n      a12 = a[6],\n      a13 = a[7];\n  var a20 = a[8],\n      a21 = a[9],\n      a22 = a[10],\n      a23 = a[11];\n  var a30 = a[12],\n      a31 = a[13],\n      a32 = a[14],\n      a33 = a[15];\n  var b00 = a00 * a11 - a01 * a10;\n  var b01 = a00 * a12 - a02 * a10;\n  var b02 = a00 * a13 - a03 * a10;\n  var b03 = a01 * a12 - a02 * a11;\n  var b04 = a01 * a13 - a03 * a11;\n  var b05 = a02 * a13 - a03 * a12;\n  var b06 = a20 * a31 - a21 * a30;\n  var b07 = a20 * a32 - a22 * a30;\n  var b08 = a20 * a33 - a23 * a30;\n  var b09 = a21 * a32 - a22 * a31;\n  var b10 = a21 * a33 - a23 * a31;\n  var b11 = a22 * a33 - a23 * a32; // Calculate the determinant\n\n  var det = b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06;\n\n  if (!det) {\n    return null;\n  }\n\n  det = 1.0 / det;\n  out[0] = (a11 * b11 - a12 * b10 + a13 * b09) * det;\n  out[1] = (a12 * b08 - a10 * b11 - a13 * b07) * det;\n  out[2] = (a10 * b10 - a11 * b08 + a13 * b06) * det;\n  out[3] = (a02 * b10 - a01 * b11 - a03 * b09) * det;\n  out[4] = (a00 * b11 - a02 * b08 + a03 * b07) * det;\n  out[5] = (a01 * b08 - a00 * b10 - a03 * b06) * det;\n  out[6] = (a31 * b05 - a32 * b04 + a33 * b03) * det;\n  out[7] = (a32 * b02 - a30 * b05 - a33 * b01) * det;\n  out[8] = (a30 * b04 - a31 * b02 + a33 * b00) * det;\n  return out;\n}\n/**\n * Generates a 2D projection matrix with the given bounds\n *\n * @param {mat3} out mat3 frustum matrix will be written into\n * @param {number} width Width of your gl context\n * @param {number} height Height of gl context\n * @returns {mat3} out\n */\n\nexport function projection(out, width, height) {\n  out[0] = 2 / width;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = -2 / height;\n  out[5] = 0;\n  out[6] = -1;\n  out[7] = 1;\n  out[8] = 1;\n  return out;\n}\n/**\n * Returns a string representation of a mat3\n *\n * @param {ReadonlyMat3} a matrix to represent as a string\n * @returns {String} string representation of the matrix\n */\n\nexport function str(a) {\n  return \"mat3(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \", \" + a[4] + \", \" + a[5] + \", \" + a[6] + \", \" + a[7] + \", \" + a[8] + \")\";\n}\n/**\n * Returns Frobenius norm of a mat3\n *\n * @param {ReadonlyMat3} a the matrix to calculate Frobenius norm of\n * @returns {Number} Frobenius norm\n */\n\nexport function frob(a) {\n  return Math.hypot(a[0], a[1], a[2], a[3], a[4], a[5], a[6], a[7], a[8]);\n}\n/**\n * Adds two mat3's\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the first operand\n * @param {ReadonlyMat3} b the second operand\n * @returns {mat3} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  out[4] = a[4] + b[4];\n  out[5] = a[5] + b[5];\n  out[6] = a[6] + b[6];\n  out[7] = a[7] + b[7];\n  out[8] = a[8] + b[8];\n  return out;\n}\n/**\n * Subtracts matrix b from matrix a\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the first operand\n * @param {ReadonlyMat3} b the second operand\n * @returns {mat3} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  out[3] = a[3] - b[3];\n  out[4] = a[4] - b[4];\n  out[5] = a[5] - b[5];\n  out[6] = a[6] - b[6];\n  out[7] = a[7] - b[7];\n  out[8] = a[8] - b[8];\n  return out;\n}\n/**\n * Multiply each element of the matrix by a scalar.\n *\n * @param {mat3} out the receiving matrix\n * @param {ReadonlyMat3} a the matrix to scale\n * @param {Number} b amount to scale the matrix's elements by\n * @returns {mat3} out\n */\n\nexport function multiplyScalar(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  out[4] = a[4] * b;\n  out[5] = a[5] * b;\n  out[6] = a[6] * b;\n  out[7] = a[7] * b;\n  out[8] = a[8] * b;\n  return out;\n}\n/**\n * Adds two mat3's after multiplying each element of the second operand by a scalar value.\n *\n * @param {mat3} out the receiving vector\n * @param {ReadonlyMat3} a the first operand\n * @param {ReadonlyMat3} b the second operand\n * @param {Number} scale the amount to scale b's elements by before adding\n * @returns {mat3} out\n */\n\nexport function multiplyScalarAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  out[3] = a[3] + b[3] * scale;\n  out[4] = a[4] + b[4] * scale;\n  out[5] = a[5] + b[5] * scale;\n  out[6] = a[6] + b[6] * scale;\n  out[7] = a[7] + b[7] * scale;\n  out[8] = a[8] + b[8] * scale;\n  return out;\n}\n/**\n * Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyMat3} a The first matrix.\n * @param {ReadonlyMat3} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3] && a[4] === b[4] && a[5] === b[5] && a[6] === b[6] && a[7] === b[7] && a[8] === b[8];\n}\n/**\n * Returns whether or not the matrices have approximately the same elements in the same position.\n *\n * @param {ReadonlyMat3} a The first matrix.\n * @param {ReadonlyMat3} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5],\n      a6 = a[6],\n      a7 = a[7],\n      a8 = a[8];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3],\n      b4 = b[4],\n      b5 = b[5],\n      b6 = b[6],\n      b7 = b[7],\n      b8 = b[8];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3)) && Math.abs(a4 - b4) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a4), Math.abs(b4)) && Math.abs(a5 - b5) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a5), Math.abs(b5)) && Math.abs(a6 - b6) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a6), Math.abs(b6)) && Math.abs(a7 - b7) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a7), Math.abs(b7)) && Math.abs(a8 - b8) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a8), Math.abs(b8));\n}\n/**\n * Alias for {@link mat3.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link mat3.subtract}\n * @function\n */\n\nexport var sub = subtract;", "import * as glMatrix from \"./common.js\";\n/**\n * 4x4 Matrix<br>Format: column-major, when typed out it looks like row-major<br>The matrices are being post multiplied.\n * @module mat4\n */\n\n/**\n * Creates a new identity mat4\n *\n * @returns {mat4} a new 4x4 matrix\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(16);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[1] = 0;\n    out[2] = 0;\n    out[3] = 0;\n    out[4] = 0;\n    out[6] = 0;\n    out[7] = 0;\n    out[8] = 0;\n    out[9] = 0;\n    out[11] = 0;\n    out[12] = 0;\n    out[13] = 0;\n    out[14] = 0;\n  }\n\n  out[0] = 1;\n  out[5] = 1;\n  out[10] = 1;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a new mat4 initialized with values from an existing matrix\n *\n * @param {ReadonlyMat4} a matrix to clone\n * @returns {mat4} a new 4x4 matrix\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(16);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  out[8] = a[8];\n  out[9] = a[9];\n  out[10] = a[10];\n  out[11] = a[11];\n  out[12] = a[12];\n  out[13] = a[13];\n  out[14] = a[14];\n  out[15] = a[15];\n  return out;\n}\n/**\n * Copy the values from one mat4 to another\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the source matrix\n * @returns {mat4} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  out[8] = a[8];\n  out[9] = a[9];\n  out[10] = a[10];\n  out[11] = a[11];\n  out[12] = a[12];\n  out[13] = a[13];\n  out[14] = a[14];\n  out[15] = a[15];\n  return out;\n}\n/**\n * Create a new mat4 with the given values\n *\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m02 Component in column 0, row 2 position (index 2)\n * @param {Number} m03 Component in column 0, row 3 position (index 3)\n * @param {Number} m10 Component in column 1, row 0 position (index 4)\n * @param {Number} m11 Component in column 1, row 1 position (index 5)\n * @param {Number} m12 Component in column 1, row 2 position (index 6)\n * @param {Number} m13 Component in column 1, row 3 position (index 7)\n * @param {Number} m20 Component in column 2, row 0 position (index 8)\n * @param {Number} m21 Component in column 2, row 1 position (index 9)\n * @param {Number} m22 Component in column 2, row 2 position (index 10)\n * @param {Number} m23 Component in column 2, row 3 position (index 11)\n * @param {Number} m30 Component in column 3, row 0 position (index 12)\n * @param {Number} m31 Component in column 3, row 1 position (index 13)\n * @param {Number} m32 Component in column 3, row 2 position (index 14)\n * @param {Number} m33 Component in column 3, row 3 position (index 15)\n * @returns {mat4} A new mat4\n */\n\nexport function fromValues(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33) {\n  var out = new glMatrix.ARRAY_TYPE(16);\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m02;\n  out[3] = m03;\n  out[4] = m10;\n  out[5] = m11;\n  out[6] = m12;\n  out[7] = m13;\n  out[8] = m20;\n  out[9] = m21;\n  out[10] = m22;\n  out[11] = m23;\n  out[12] = m30;\n  out[13] = m31;\n  out[14] = m32;\n  out[15] = m33;\n  return out;\n}\n/**\n * Set the components of a mat4 to the given values\n *\n * @param {mat4} out the receiving matrix\n * @param {Number} m00 Component in column 0, row 0 position (index 0)\n * @param {Number} m01 Component in column 0, row 1 position (index 1)\n * @param {Number} m02 Component in column 0, row 2 position (index 2)\n * @param {Number} m03 Component in column 0, row 3 position (index 3)\n * @param {Number} m10 Component in column 1, row 0 position (index 4)\n * @param {Number} m11 Component in column 1, row 1 position (index 5)\n * @param {Number} m12 Component in column 1, row 2 position (index 6)\n * @param {Number} m13 Component in column 1, row 3 position (index 7)\n * @param {Number} m20 Component in column 2, row 0 position (index 8)\n * @param {Number} m21 Component in column 2, row 1 position (index 9)\n * @param {Number} m22 Component in column 2, row 2 position (index 10)\n * @param {Number} m23 Component in column 2, row 3 position (index 11)\n * @param {Number} m30 Component in column 3, row 0 position (index 12)\n * @param {Number} m31 Component in column 3, row 1 position (index 13)\n * @param {Number} m32 Component in column 3, row 2 position (index 14)\n * @param {Number} m33 Component in column 3, row 3 position (index 15)\n * @returns {mat4} out\n */\n\nexport function set(out, m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33) {\n  out[0] = m00;\n  out[1] = m01;\n  out[2] = m02;\n  out[3] = m03;\n  out[4] = m10;\n  out[5] = m11;\n  out[6] = m12;\n  out[7] = m13;\n  out[8] = m20;\n  out[9] = m21;\n  out[10] = m22;\n  out[11] = m23;\n  out[12] = m30;\n  out[13] = m31;\n  out[14] = m32;\n  out[15] = m33;\n  return out;\n}\n/**\n * Set a mat4 to the identity matrix\n *\n * @param {mat4} out the receiving matrix\n * @returns {mat4} out\n */\n\nexport function identity(out) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = 1;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = 1;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Transpose the values of a mat4\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the source matrix\n * @returns {mat4} out\n */\n\nexport function transpose(out, a) {\n  // If we are transposing ourselves we can skip a few steps but have to cache some values\n  if (out === a) {\n    var a01 = a[1],\n        a02 = a[2],\n        a03 = a[3];\n    var a12 = a[6],\n        a13 = a[7];\n    var a23 = a[11];\n    out[1] = a[4];\n    out[2] = a[8];\n    out[3] = a[12];\n    out[4] = a01;\n    out[6] = a[9];\n    out[7] = a[13];\n    out[8] = a02;\n    out[9] = a12;\n    out[11] = a[14];\n    out[12] = a03;\n    out[13] = a13;\n    out[14] = a23;\n  } else {\n    out[0] = a[0];\n    out[1] = a[4];\n    out[2] = a[8];\n    out[3] = a[12];\n    out[4] = a[1];\n    out[5] = a[5];\n    out[6] = a[9];\n    out[7] = a[13];\n    out[8] = a[2];\n    out[9] = a[6];\n    out[10] = a[10];\n    out[11] = a[14];\n    out[12] = a[3];\n    out[13] = a[7];\n    out[14] = a[11];\n    out[15] = a[15];\n  }\n\n  return out;\n}\n/**\n * Inverts a mat4\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the source matrix\n * @returns {mat4} out\n */\n\nexport function invert(out, a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a03 = a[3];\n  var a10 = a[4],\n      a11 = a[5],\n      a12 = a[6],\n      a13 = a[7];\n  var a20 = a[8],\n      a21 = a[9],\n      a22 = a[10],\n      a23 = a[11];\n  var a30 = a[12],\n      a31 = a[13],\n      a32 = a[14],\n      a33 = a[15];\n  var b00 = a00 * a11 - a01 * a10;\n  var b01 = a00 * a12 - a02 * a10;\n  var b02 = a00 * a13 - a03 * a10;\n  var b03 = a01 * a12 - a02 * a11;\n  var b04 = a01 * a13 - a03 * a11;\n  var b05 = a02 * a13 - a03 * a12;\n  var b06 = a20 * a31 - a21 * a30;\n  var b07 = a20 * a32 - a22 * a30;\n  var b08 = a20 * a33 - a23 * a30;\n  var b09 = a21 * a32 - a22 * a31;\n  var b10 = a21 * a33 - a23 * a31;\n  var b11 = a22 * a33 - a23 * a32; // Calculate the determinant\n\n  var det = b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06;\n\n  if (!det) {\n    return null;\n  }\n\n  det = 1.0 / det;\n  out[0] = (a11 * b11 - a12 * b10 + a13 * b09) * det;\n  out[1] = (a02 * b10 - a01 * b11 - a03 * b09) * det;\n  out[2] = (a31 * b05 - a32 * b04 + a33 * b03) * det;\n  out[3] = (a22 * b04 - a21 * b05 - a23 * b03) * det;\n  out[4] = (a12 * b08 - a10 * b11 - a13 * b07) * det;\n  out[5] = (a00 * b11 - a02 * b08 + a03 * b07) * det;\n  out[6] = (a32 * b02 - a30 * b05 - a33 * b01) * det;\n  out[7] = (a20 * b05 - a22 * b02 + a23 * b01) * det;\n  out[8] = (a10 * b10 - a11 * b08 + a13 * b06) * det;\n  out[9] = (a01 * b08 - a00 * b10 - a03 * b06) * det;\n  out[10] = (a30 * b04 - a31 * b02 + a33 * b00) * det;\n  out[11] = (a21 * b02 - a20 * b04 - a23 * b00) * det;\n  out[12] = (a11 * b07 - a10 * b09 - a12 * b06) * det;\n  out[13] = (a00 * b09 - a01 * b07 + a02 * b06) * det;\n  out[14] = (a31 * b01 - a30 * b03 - a32 * b00) * det;\n  out[15] = (a20 * b03 - a21 * b01 + a22 * b00) * det;\n  return out;\n}\n/**\n * Calculates the adjugate of a mat4\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the source matrix\n * @returns {mat4} out\n */\n\nexport function adjoint(out, a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a03 = a[3];\n  var a10 = a[4],\n      a11 = a[5],\n      a12 = a[6],\n      a13 = a[7];\n  var a20 = a[8],\n      a21 = a[9],\n      a22 = a[10],\n      a23 = a[11];\n  var a30 = a[12],\n      a31 = a[13],\n      a32 = a[14],\n      a33 = a[15];\n  out[0] = a11 * (a22 * a33 - a23 * a32) - a21 * (a12 * a33 - a13 * a32) + a31 * (a12 * a23 - a13 * a22);\n  out[1] = -(a01 * (a22 * a33 - a23 * a32) - a21 * (a02 * a33 - a03 * a32) + a31 * (a02 * a23 - a03 * a22));\n  out[2] = a01 * (a12 * a33 - a13 * a32) - a11 * (a02 * a33 - a03 * a32) + a31 * (a02 * a13 - a03 * a12);\n  out[3] = -(a01 * (a12 * a23 - a13 * a22) - a11 * (a02 * a23 - a03 * a22) + a21 * (a02 * a13 - a03 * a12));\n  out[4] = -(a10 * (a22 * a33 - a23 * a32) - a20 * (a12 * a33 - a13 * a32) + a30 * (a12 * a23 - a13 * a22));\n  out[5] = a00 * (a22 * a33 - a23 * a32) - a20 * (a02 * a33 - a03 * a32) + a30 * (a02 * a23 - a03 * a22);\n  out[6] = -(a00 * (a12 * a33 - a13 * a32) - a10 * (a02 * a33 - a03 * a32) + a30 * (a02 * a13 - a03 * a12));\n  out[7] = a00 * (a12 * a23 - a13 * a22) - a10 * (a02 * a23 - a03 * a22) + a20 * (a02 * a13 - a03 * a12);\n  out[8] = a10 * (a21 * a33 - a23 * a31) - a20 * (a11 * a33 - a13 * a31) + a30 * (a11 * a23 - a13 * a21);\n  out[9] = -(a00 * (a21 * a33 - a23 * a31) - a20 * (a01 * a33 - a03 * a31) + a30 * (a01 * a23 - a03 * a21));\n  out[10] = a00 * (a11 * a33 - a13 * a31) - a10 * (a01 * a33 - a03 * a31) + a30 * (a01 * a13 - a03 * a11);\n  out[11] = -(a00 * (a11 * a23 - a13 * a21) - a10 * (a01 * a23 - a03 * a21) + a20 * (a01 * a13 - a03 * a11));\n  out[12] = -(a10 * (a21 * a32 - a22 * a31) - a20 * (a11 * a32 - a12 * a31) + a30 * (a11 * a22 - a12 * a21));\n  out[13] = a00 * (a21 * a32 - a22 * a31) - a20 * (a01 * a32 - a02 * a31) + a30 * (a01 * a22 - a02 * a21);\n  out[14] = -(a00 * (a11 * a32 - a12 * a31) - a10 * (a01 * a32 - a02 * a31) + a30 * (a01 * a12 - a02 * a11));\n  out[15] = a00 * (a11 * a22 - a12 * a21) - a10 * (a01 * a22 - a02 * a21) + a20 * (a01 * a12 - a02 * a11);\n  return out;\n}\n/**\n * Calculates the determinant of a mat4\n *\n * @param {ReadonlyMat4} a the source matrix\n * @returns {Number} determinant of a\n */\n\nexport function determinant(a) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a03 = a[3];\n  var a10 = a[4],\n      a11 = a[5],\n      a12 = a[6],\n      a13 = a[7];\n  var a20 = a[8],\n      a21 = a[9],\n      a22 = a[10],\n      a23 = a[11];\n  var a30 = a[12],\n      a31 = a[13],\n      a32 = a[14],\n      a33 = a[15];\n  var b00 = a00 * a11 - a01 * a10;\n  var b01 = a00 * a12 - a02 * a10;\n  var b02 = a00 * a13 - a03 * a10;\n  var b03 = a01 * a12 - a02 * a11;\n  var b04 = a01 * a13 - a03 * a11;\n  var b05 = a02 * a13 - a03 * a12;\n  var b06 = a20 * a31 - a21 * a30;\n  var b07 = a20 * a32 - a22 * a30;\n  var b08 = a20 * a33 - a23 * a30;\n  var b09 = a21 * a32 - a22 * a31;\n  var b10 = a21 * a33 - a23 * a31;\n  var b11 = a22 * a33 - a23 * a32; // Calculate the determinant\n\n  return b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06;\n}\n/**\n * Multiplies two mat4s\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the first operand\n * @param {ReadonlyMat4} b the second operand\n * @returns {mat4} out\n */\n\nexport function multiply(out, a, b) {\n  var a00 = a[0],\n      a01 = a[1],\n      a02 = a[2],\n      a03 = a[3];\n  var a10 = a[4],\n      a11 = a[5],\n      a12 = a[6],\n      a13 = a[7];\n  var a20 = a[8],\n      a21 = a[9],\n      a22 = a[10],\n      a23 = a[11];\n  var a30 = a[12],\n      a31 = a[13],\n      a32 = a[14],\n      a33 = a[15]; // Cache only the current line of the second matrix\n\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3];\n  out[0] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;\n  out[1] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;\n  out[2] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;\n  out[3] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;\n  b0 = b[4];\n  b1 = b[5];\n  b2 = b[6];\n  b3 = b[7];\n  out[4] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;\n  out[5] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;\n  out[6] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;\n  out[7] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;\n  b0 = b[8];\n  b1 = b[9];\n  b2 = b[10];\n  b3 = b[11];\n  out[8] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;\n  out[9] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;\n  out[10] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;\n  out[11] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;\n  b0 = b[12];\n  b1 = b[13];\n  b2 = b[14];\n  b3 = b[15];\n  out[12] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;\n  out[13] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;\n  out[14] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;\n  out[15] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;\n  return out;\n}\n/**\n * Translate a mat4 by the given vector\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to translate\n * @param {ReadonlyVec3} v vector to translate by\n * @returns {mat4} out\n */\n\nexport function translate(out, a, v) {\n  var x = v[0],\n      y = v[1],\n      z = v[2];\n  var a00, a01, a02, a03;\n  var a10, a11, a12, a13;\n  var a20, a21, a22, a23;\n\n  if (a === out) {\n    out[12] = a[0] * x + a[4] * y + a[8] * z + a[12];\n    out[13] = a[1] * x + a[5] * y + a[9] * z + a[13];\n    out[14] = a[2] * x + a[6] * y + a[10] * z + a[14];\n    out[15] = a[3] * x + a[7] * y + a[11] * z + a[15];\n  } else {\n    a00 = a[0];\n    a01 = a[1];\n    a02 = a[2];\n    a03 = a[3];\n    a10 = a[4];\n    a11 = a[5];\n    a12 = a[6];\n    a13 = a[7];\n    a20 = a[8];\n    a21 = a[9];\n    a22 = a[10];\n    a23 = a[11];\n    out[0] = a00;\n    out[1] = a01;\n    out[2] = a02;\n    out[3] = a03;\n    out[4] = a10;\n    out[5] = a11;\n    out[6] = a12;\n    out[7] = a13;\n    out[8] = a20;\n    out[9] = a21;\n    out[10] = a22;\n    out[11] = a23;\n    out[12] = a00 * x + a10 * y + a20 * z + a[12];\n    out[13] = a01 * x + a11 * y + a21 * z + a[13];\n    out[14] = a02 * x + a12 * y + a22 * z + a[14];\n    out[15] = a03 * x + a13 * y + a23 * z + a[15];\n  }\n\n  return out;\n}\n/**\n * Scales the mat4 by the dimensions in the given vec3 not using vectorization\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to scale\n * @param {ReadonlyVec3} v the vec3 to scale the matrix by\n * @returns {mat4} out\n **/\n\nexport function scale(out, a, v) {\n  var x = v[0],\n      y = v[1],\n      z = v[2];\n  out[0] = a[0] * x;\n  out[1] = a[1] * x;\n  out[2] = a[2] * x;\n  out[3] = a[3] * x;\n  out[4] = a[4] * y;\n  out[5] = a[5] * y;\n  out[6] = a[6] * y;\n  out[7] = a[7] * y;\n  out[8] = a[8] * z;\n  out[9] = a[9] * z;\n  out[10] = a[10] * z;\n  out[11] = a[11] * z;\n  out[12] = a[12];\n  out[13] = a[13];\n  out[14] = a[14];\n  out[15] = a[15];\n  return out;\n}\n/**\n * Rotates a mat4 by the given angle around the given axis\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @param {ReadonlyVec3} axis the axis to rotate around\n * @returns {mat4} out\n */\n\nexport function rotate(out, a, rad, axis) {\n  var x = axis[0],\n      y = axis[1],\n      z = axis[2];\n  var len = Math.hypot(x, y, z);\n  var s, c, t;\n  var a00, a01, a02, a03;\n  var a10, a11, a12, a13;\n  var a20, a21, a22, a23;\n  var b00, b01, b02;\n  var b10, b11, b12;\n  var b20, b21, b22;\n\n  if (len < glMatrix.EPSILON) {\n    return null;\n  }\n\n  len = 1 / len;\n  x *= len;\n  y *= len;\n  z *= len;\n  s = Math.sin(rad);\n  c = Math.cos(rad);\n  t = 1 - c;\n  a00 = a[0];\n  a01 = a[1];\n  a02 = a[2];\n  a03 = a[3];\n  a10 = a[4];\n  a11 = a[5];\n  a12 = a[6];\n  a13 = a[7];\n  a20 = a[8];\n  a21 = a[9];\n  a22 = a[10];\n  a23 = a[11]; // Construct the elements of the rotation matrix\n\n  b00 = x * x * t + c;\n  b01 = y * x * t + z * s;\n  b02 = z * x * t - y * s;\n  b10 = x * y * t - z * s;\n  b11 = y * y * t + c;\n  b12 = z * y * t + x * s;\n  b20 = x * z * t + y * s;\n  b21 = y * z * t - x * s;\n  b22 = z * z * t + c; // Perform rotation-specific matrix multiplication\n\n  out[0] = a00 * b00 + a10 * b01 + a20 * b02;\n  out[1] = a01 * b00 + a11 * b01 + a21 * b02;\n  out[2] = a02 * b00 + a12 * b01 + a22 * b02;\n  out[3] = a03 * b00 + a13 * b01 + a23 * b02;\n  out[4] = a00 * b10 + a10 * b11 + a20 * b12;\n  out[5] = a01 * b10 + a11 * b11 + a21 * b12;\n  out[6] = a02 * b10 + a12 * b11 + a22 * b12;\n  out[7] = a03 * b10 + a13 * b11 + a23 * b12;\n  out[8] = a00 * b20 + a10 * b21 + a20 * b22;\n  out[9] = a01 * b20 + a11 * b21 + a21 * b22;\n  out[10] = a02 * b20 + a12 * b21 + a22 * b22;\n  out[11] = a03 * b20 + a13 * b21 + a23 * b22;\n\n  if (a !== out) {\n    // If the source and destination differ, copy the unchanged last row\n    out[12] = a[12];\n    out[13] = a[13];\n    out[14] = a[14];\n    out[15] = a[15];\n  }\n\n  return out;\n}\n/**\n * Rotates a matrix by the given angle around the X axis\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function rotateX(out, a, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  var a10 = a[4];\n  var a11 = a[5];\n  var a12 = a[6];\n  var a13 = a[7];\n  var a20 = a[8];\n  var a21 = a[9];\n  var a22 = a[10];\n  var a23 = a[11];\n\n  if (a !== out) {\n    // If the source and destination differ, copy the unchanged rows\n    out[0] = a[0];\n    out[1] = a[1];\n    out[2] = a[2];\n    out[3] = a[3];\n    out[12] = a[12];\n    out[13] = a[13];\n    out[14] = a[14];\n    out[15] = a[15];\n  } // Perform axis-specific matrix multiplication\n\n\n  out[4] = a10 * c + a20 * s;\n  out[5] = a11 * c + a21 * s;\n  out[6] = a12 * c + a22 * s;\n  out[7] = a13 * c + a23 * s;\n  out[8] = a20 * c - a10 * s;\n  out[9] = a21 * c - a11 * s;\n  out[10] = a22 * c - a12 * s;\n  out[11] = a23 * c - a13 * s;\n  return out;\n}\n/**\n * Rotates a matrix by the given angle around the Y axis\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function rotateY(out, a, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  var a00 = a[0];\n  var a01 = a[1];\n  var a02 = a[2];\n  var a03 = a[3];\n  var a20 = a[8];\n  var a21 = a[9];\n  var a22 = a[10];\n  var a23 = a[11];\n\n  if (a !== out) {\n    // If the source and destination differ, copy the unchanged rows\n    out[4] = a[4];\n    out[5] = a[5];\n    out[6] = a[6];\n    out[7] = a[7];\n    out[12] = a[12];\n    out[13] = a[13];\n    out[14] = a[14];\n    out[15] = a[15];\n  } // Perform axis-specific matrix multiplication\n\n\n  out[0] = a00 * c - a20 * s;\n  out[1] = a01 * c - a21 * s;\n  out[2] = a02 * c - a22 * s;\n  out[3] = a03 * c - a23 * s;\n  out[8] = a00 * s + a20 * c;\n  out[9] = a01 * s + a21 * c;\n  out[10] = a02 * s + a22 * c;\n  out[11] = a03 * s + a23 * c;\n  return out;\n}\n/**\n * Rotates a matrix by the given angle around the Z axis\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to rotate\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function rotateZ(out, a, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad);\n  var a00 = a[0];\n  var a01 = a[1];\n  var a02 = a[2];\n  var a03 = a[3];\n  var a10 = a[4];\n  var a11 = a[5];\n  var a12 = a[6];\n  var a13 = a[7];\n\n  if (a !== out) {\n    // If the source and destination differ, copy the unchanged last row\n    out[8] = a[8];\n    out[9] = a[9];\n    out[10] = a[10];\n    out[11] = a[11];\n    out[12] = a[12];\n    out[13] = a[13];\n    out[14] = a[14];\n    out[15] = a[15];\n  } // Perform axis-specific matrix multiplication\n\n\n  out[0] = a00 * c + a10 * s;\n  out[1] = a01 * c + a11 * s;\n  out[2] = a02 * c + a12 * s;\n  out[3] = a03 * c + a13 * s;\n  out[4] = a10 * c - a00 * s;\n  out[5] = a11 * c - a01 * s;\n  out[6] = a12 * c - a02 * s;\n  out[7] = a13 * c - a03 * s;\n  return out;\n}\n/**\n * Creates a matrix from a vector translation\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.translate(dest, dest, vec);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {ReadonlyVec3} v Translation vector\n * @returns {mat4} out\n */\n\nexport function fromTranslation(out, v) {\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = 1;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = 1;\n  out[11] = 0;\n  out[12] = v[0];\n  out[13] = v[1];\n  out[14] = v[2];\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a vector scaling\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.scale(dest, dest, vec);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {ReadonlyVec3} v Scaling vector\n * @returns {mat4} out\n */\n\nexport function fromScaling(out, v) {\n  out[0] = v[0];\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = v[1];\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = v[2];\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a given angle around a given axis\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.rotate(dest, dest, rad, axis);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @param {ReadonlyVec3} axis the axis to rotate around\n * @returns {mat4} out\n */\n\nexport function fromRotation(out, rad, axis) {\n  var x = axis[0],\n      y = axis[1],\n      z = axis[2];\n  var len = Math.hypot(x, y, z);\n  var s, c, t;\n\n  if (len < glMatrix.EPSILON) {\n    return null;\n  }\n\n  len = 1 / len;\n  x *= len;\n  y *= len;\n  z *= len;\n  s = Math.sin(rad);\n  c = Math.cos(rad);\n  t = 1 - c; // Perform rotation-specific matrix multiplication\n\n  out[0] = x * x * t + c;\n  out[1] = y * x * t + z * s;\n  out[2] = z * x * t - y * s;\n  out[3] = 0;\n  out[4] = x * y * t - z * s;\n  out[5] = y * y * t + c;\n  out[6] = z * y * t + x * s;\n  out[7] = 0;\n  out[8] = x * z * t + y * s;\n  out[9] = y * z * t - x * s;\n  out[10] = z * z * t + c;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from the given angle around the X axis\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.rotateX(dest, dest, rad);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function fromXRotation(out, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad); // Perform axis-specific matrix multiplication\n\n  out[0] = 1;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = c;\n  out[6] = s;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = -s;\n  out[10] = c;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from the given angle around the Y axis\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.rotateY(dest, dest, rad);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function fromYRotation(out, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad); // Perform axis-specific matrix multiplication\n\n  out[0] = c;\n  out[1] = 0;\n  out[2] = -s;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = 1;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = s;\n  out[9] = 0;\n  out[10] = c;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from the given angle around the Z axis\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.rotateZ(dest, dest, rad);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {Number} rad the angle to rotate the matrix by\n * @returns {mat4} out\n */\n\nexport function fromZRotation(out, rad) {\n  var s = Math.sin(rad);\n  var c = Math.cos(rad); // Perform axis-specific matrix multiplication\n\n  out[0] = c;\n  out[1] = s;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = -s;\n  out[5] = c;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = 1;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a quaternion rotation and vector translation\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.translate(dest, vec);\n *     let quatMat = mat4.create();\n *     quat4.toMat4(quat, quatMat);\n *     mat4.multiply(dest, quatMat);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {quat4} q Rotation quaternion\n * @param {ReadonlyVec3} v Translation vector\n * @returns {mat4} out\n */\n\nexport function fromRotationTranslation(out, q, v) {\n  // Quaternion math\n  var x = q[0],\n      y = q[1],\n      z = q[2],\n      w = q[3];\n  var x2 = x + x;\n  var y2 = y + y;\n  var z2 = z + z;\n  var xx = x * x2;\n  var xy = x * y2;\n  var xz = x * z2;\n  var yy = y * y2;\n  var yz = y * z2;\n  var zz = z * z2;\n  var wx = w * x2;\n  var wy = w * y2;\n  var wz = w * z2;\n  out[0] = 1 - (yy + zz);\n  out[1] = xy + wz;\n  out[2] = xz - wy;\n  out[3] = 0;\n  out[4] = xy - wz;\n  out[5] = 1 - (xx + zz);\n  out[6] = yz + wx;\n  out[7] = 0;\n  out[8] = xz + wy;\n  out[9] = yz - wx;\n  out[10] = 1 - (xx + yy);\n  out[11] = 0;\n  out[12] = v[0];\n  out[13] = v[1];\n  out[14] = v[2];\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a new mat4 from a dual quat.\n *\n * @param {mat4} out Matrix\n * @param {ReadonlyQuat2} a Dual Quaternion\n * @returns {mat4} mat4 receiving operation result\n */\n\nexport function fromQuat2(out, a) {\n  var translation = new glMatrix.ARRAY_TYPE(3);\n  var bx = -a[0],\n      by = -a[1],\n      bz = -a[2],\n      bw = a[3],\n      ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7];\n  var magnitude = bx * bx + by * by + bz * bz + bw * bw; //Only scale if it makes sense\n\n  if (magnitude > 0) {\n    translation[0] = (ax * bw + aw * bx + ay * bz - az * by) * 2 / magnitude;\n    translation[1] = (ay * bw + aw * by + az * bx - ax * bz) * 2 / magnitude;\n    translation[2] = (az * bw + aw * bz + ax * by - ay * bx) * 2 / magnitude;\n  } else {\n    translation[0] = (ax * bw + aw * bx + ay * bz - az * by) * 2;\n    translation[1] = (ay * bw + aw * by + az * bx - ax * bz) * 2;\n    translation[2] = (az * bw + aw * bz + ax * by - ay * bx) * 2;\n  }\n\n  fromRotationTranslation(out, a, translation);\n  return out;\n}\n/**\n * Returns the translation vector component of a transformation\n *  matrix. If a matrix is built with fromRotationTranslation,\n *  the returned vector will be the same as the translation vector\n *  originally supplied.\n * @param  {vec3} out Vector to receive translation component\n * @param  {ReadonlyMat4} mat Matrix to be decomposed (input)\n * @return {vec3} out\n */\n\nexport function getTranslation(out, mat) {\n  out[0] = mat[12];\n  out[1] = mat[13];\n  out[2] = mat[14];\n  return out;\n}\n/**\n * Returns the scaling factor component of a transformation\n *  matrix. If a matrix is built with fromRotationTranslationScale\n *  with a normalized Quaternion paramter, the returned vector will be\n *  the same as the scaling vector\n *  originally supplied.\n * @param  {vec3} out Vector to receive scaling factor component\n * @param  {ReadonlyMat4} mat Matrix to be decomposed (input)\n * @return {vec3} out\n */\n\nexport function getScaling(out, mat) {\n  var m11 = mat[0];\n  var m12 = mat[1];\n  var m13 = mat[2];\n  var m21 = mat[4];\n  var m22 = mat[5];\n  var m23 = mat[6];\n  var m31 = mat[8];\n  var m32 = mat[9];\n  var m33 = mat[10];\n  out[0] = Math.hypot(m11, m12, m13);\n  out[1] = Math.hypot(m21, m22, m23);\n  out[2] = Math.hypot(m31, m32, m33);\n  return out;\n}\n/**\n * Returns a quaternion representing the rotational component\n *  of a transformation matrix. If a matrix is built with\n *  fromRotationTranslation, the returned quaternion will be the\n *  same as the quaternion originally supplied.\n * @param {quat} out Quaternion to receive the rotation component\n * @param {ReadonlyMat4} mat Matrix to be decomposed (input)\n * @return {quat} out\n */\n\nexport function getRotation(out, mat) {\n  var scaling = new glMatrix.ARRAY_TYPE(3);\n  getScaling(scaling, mat);\n  var is1 = 1 / scaling[0];\n  var is2 = 1 / scaling[1];\n  var is3 = 1 / scaling[2];\n  var sm11 = mat[0] * is1;\n  var sm12 = mat[1] * is2;\n  var sm13 = mat[2] * is3;\n  var sm21 = mat[4] * is1;\n  var sm22 = mat[5] * is2;\n  var sm23 = mat[6] * is3;\n  var sm31 = mat[8] * is1;\n  var sm32 = mat[9] * is2;\n  var sm33 = mat[10] * is3;\n  var trace = sm11 + sm22 + sm33;\n  var S = 0;\n\n  if (trace > 0) {\n    S = Math.sqrt(trace + 1.0) * 2;\n    out[3] = 0.25 * S;\n    out[0] = (sm23 - sm32) / S;\n    out[1] = (sm31 - sm13) / S;\n    out[2] = (sm12 - sm21) / S;\n  } else if (sm11 > sm22 && sm11 > sm33) {\n    S = Math.sqrt(1.0 + sm11 - sm22 - sm33) * 2;\n    out[3] = (sm23 - sm32) / S;\n    out[0] = 0.25 * S;\n    out[1] = (sm12 + sm21) / S;\n    out[2] = (sm31 + sm13) / S;\n  } else if (sm22 > sm33) {\n    S = Math.sqrt(1.0 + sm22 - sm11 - sm33) * 2;\n    out[3] = (sm31 - sm13) / S;\n    out[0] = (sm12 + sm21) / S;\n    out[1] = 0.25 * S;\n    out[2] = (sm23 + sm32) / S;\n  } else {\n    S = Math.sqrt(1.0 + sm33 - sm11 - sm22) * 2;\n    out[3] = (sm12 - sm21) / S;\n    out[0] = (sm31 + sm13) / S;\n    out[1] = (sm23 + sm32) / S;\n    out[2] = 0.25 * S;\n  }\n\n  return out;\n}\n/**\n * Creates a matrix from a quaternion rotation, vector translation and vector scale\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.translate(dest, vec);\n *     let quatMat = mat4.create();\n *     quat4.toMat4(quat, quatMat);\n *     mat4.multiply(dest, quatMat);\n *     mat4.scale(dest, scale)\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {quat4} q Rotation quaternion\n * @param {ReadonlyVec3} v Translation vector\n * @param {ReadonlyVec3} s Scaling vector\n * @returns {mat4} out\n */\n\nexport function fromRotationTranslationScale(out, q, v, s) {\n  // Quaternion math\n  var x = q[0],\n      y = q[1],\n      z = q[2],\n      w = q[3];\n  var x2 = x + x;\n  var y2 = y + y;\n  var z2 = z + z;\n  var xx = x * x2;\n  var xy = x * y2;\n  var xz = x * z2;\n  var yy = y * y2;\n  var yz = y * z2;\n  var zz = z * z2;\n  var wx = w * x2;\n  var wy = w * y2;\n  var wz = w * z2;\n  var sx = s[0];\n  var sy = s[1];\n  var sz = s[2];\n  out[0] = (1 - (yy + zz)) * sx;\n  out[1] = (xy + wz) * sx;\n  out[2] = (xz - wy) * sx;\n  out[3] = 0;\n  out[4] = (xy - wz) * sy;\n  out[5] = (1 - (xx + zz)) * sy;\n  out[6] = (yz + wx) * sy;\n  out[7] = 0;\n  out[8] = (xz + wy) * sz;\n  out[9] = (yz - wx) * sz;\n  out[10] = (1 - (xx + yy)) * sz;\n  out[11] = 0;\n  out[12] = v[0];\n  out[13] = v[1];\n  out[14] = v[2];\n  out[15] = 1;\n  return out;\n}\n/**\n * Creates a matrix from a quaternion rotation, vector translation and vector scale, rotating and scaling around the given origin\n * This is equivalent to (but much faster than):\n *\n *     mat4.identity(dest);\n *     mat4.translate(dest, vec);\n *     mat4.translate(dest, origin);\n *     let quatMat = mat4.create();\n *     quat4.toMat4(quat, quatMat);\n *     mat4.multiply(dest, quatMat);\n *     mat4.scale(dest, scale)\n *     mat4.translate(dest, negativeOrigin);\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {quat4} q Rotation quaternion\n * @param {ReadonlyVec3} v Translation vector\n * @param {ReadonlyVec3} s Scaling vector\n * @param {ReadonlyVec3} o The origin vector around which to scale and rotate\n * @returns {mat4} out\n */\n\nexport function fromRotationTranslationScaleOrigin(out, q, v, s, o) {\n  // Quaternion math\n  var x = q[0],\n      y = q[1],\n      z = q[2],\n      w = q[3];\n  var x2 = x + x;\n  var y2 = y + y;\n  var z2 = z + z;\n  var xx = x * x2;\n  var xy = x * y2;\n  var xz = x * z2;\n  var yy = y * y2;\n  var yz = y * z2;\n  var zz = z * z2;\n  var wx = w * x2;\n  var wy = w * y2;\n  var wz = w * z2;\n  var sx = s[0];\n  var sy = s[1];\n  var sz = s[2];\n  var ox = o[0];\n  var oy = o[1];\n  var oz = o[2];\n  var out0 = (1 - (yy + zz)) * sx;\n  var out1 = (xy + wz) * sx;\n  var out2 = (xz - wy) * sx;\n  var out4 = (xy - wz) * sy;\n  var out5 = (1 - (xx + zz)) * sy;\n  var out6 = (yz + wx) * sy;\n  var out8 = (xz + wy) * sz;\n  var out9 = (yz - wx) * sz;\n  var out10 = (1 - (xx + yy)) * sz;\n  out[0] = out0;\n  out[1] = out1;\n  out[2] = out2;\n  out[3] = 0;\n  out[4] = out4;\n  out[5] = out5;\n  out[6] = out6;\n  out[7] = 0;\n  out[8] = out8;\n  out[9] = out9;\n  out[10] = out10;\n  out[11] = 0;\n  out[12] = v[0] + ox - (out0 * ox + out4 * oy + out8 * oz);\n  out[13] = v[1] + oy - (out1 * ox + out5 * oy + out9 * oz);\n  out[14] = v[2] + oz - (out2 * ox + out6 * oy + out10 * oz);\n  out[15] = 1;\n  return out;\n}\n/**\n * Calculates a 4x4 matrix from the given quaternion\n *\n * @param {mat4} out mat4 receiving operation result\n * @param {ReadonlyQuat} q Quaternion to create matrix from\n *\n * @returns {mat4} out\n */\n\nexport function fromQuat(out, q) {\n  var x = q[0],\n      y = q[1],\n      z = q[2],\n      w = q[3];\n  var x2 = x + x;\n  var y2 = y + y;\n  var z2 = z + z;\n  var xx = x * x2;\n  var yx = y * x2;\n  var yy = y * y2;\n  var zx = z * x2;\n  var zy = z * y2;\n  var zz = z * z2;\n  var wx = w * x2;\n  var wy = w * y2;\n  var wz = w * z2;\n  out[0] = 1 - yy - zz;\n  out[1] = yx + wz;\n  out[2] = zx - wy;\n  out[3] = 0;\n  out[4] = yx - wz;\n  out[5] = 1 - xx - zz;\n  out[6] = zy + wx;\n  out[7] = 0;\n  out[8] = zx + wy;\n  out[9] = zy - wx;\n  out[10] = 1 - xx - yy;\n  out[11] = 0;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = 0;\n  out[15] = 1;\n  return out;\n}\n/**\n * Generates a frustum matrix with the given bounds\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {Number} left Left bound of the frustum\n * @param {Number} right Right bound of the frustum\n * @param {Number} bottom Bottom bound of the frustum\n * @param {Number} top Top bound of the frustum\n * @param {Number} near Near bound of the frustum\n * @param {Number} far Far bound of the frustum\n * @returns {mat4} out\n */\n\nexport function frustum(out, left, right, bottom, top, near, far) {\n  var rl = 1 / (right - left);\n  var tb = 1 / (top - bottom);\n  var nf = 1 / (near - far);\n  out[0] = near * 2 * rl;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = near * 2 * tb;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = (right + left) * rl;\n  out[9] = (top + bottom) * tb;\n  out[10] = (far + near) * nf;\n  out[11] = -1;\n  out[12] = 0;\n  out[13] = 0;\n  out[14] = far * near * 2 * nf;\n  out[15] = 0;\n  return out;\n}\n/**\n * Generates a perspective projection matrix with the given bounds.\n * The near/far clip planes correspond to a normalized device coordinate Z range of [-1, 1],\n * which matches WebGL/OpenGL's clip volume.\n * Passing null/undefined/no value for far will generate infinite projection matrix.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {number} fovy Vertical field of view in radians\n * @param {number} aspect Aspect ratio. typically viewport width/height\n * @param {number} near Near bound of the frustum\n * @param {number} far Far bound of the frustum, can be null or Infinity\n * @returns {mat4} out\n */\n\nexport function perspectiveNO(out, fovy, aspect, near, far) {\n  var f = 1.0 / Math.tan(fovy / 2),\n      nf;\n  out[0] = f / aspect;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = f;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[11] = -1;\n  out[12] = 0;\n  out[13] = 0;\n  out[15] = 0;\n\n  if (far != null && far !== Infinity) {\n    nf = 1 / (near - far);\n    out[10] = (far + near) * nf;\n    out[14] = 2 * far * near * nf;\n  } else {\n    out[10] = -1;\n    out[14] = -2 * near;\n  }\n\n  return out;\n}\n/**\n * Alias for {@link mat4.perspectiveNO}\n * @function\n */\n\nexport var perspective = perspectiveNO;\n/**\n * Generates a perspective projection matrix suitable for WebGPU with the given bounds.\n * The near/far clip planes correspond to a normalized device coordinate Z range of [0, 1],\n * which matches WebGPU/Vulkan/DirectX/Metal's clip volume.\n * Passing null/undefined/no value for far will generate infinite projection matrix.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {number} fovy Vertical field of view in radians\n * @param {number} aspect Aspect ratio. typically viewport width/height\n * @param {number} near Near bound of the frustum\n * @param {number} far Far bound of the frustum, can be null or Infinity\n * @returns {mat4} out\n */\n\nexport function perspectiveZO(out, fovy, aspect, near, far) {\n  var f = 1.0 / Math.tan(fovy / 2),\n      nf;\n  out[0] = f / aspect;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = f;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[11] = -1;\n  out[12] = 0;\n  out[13] = 0;\n  out[15] = 0;\n\n  if (far != null && far !== Infinity) {\n    nf = 1 / (near - far);\n    out[10] = far * nf;\n    out[14] = far * near * nf;\n  } else {\n    out[10] = -1;\n    out[14] = -near;\n  }\n\n  return out;\n}\n/**\n * Generates a perspective projection matrix with the given field of view.\n * This is primarily useful for generating projection matrices to be used\n * with the still experiemental WebVR API.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {Object} fov Object containing the following values: upDegrees, downDegrees, leftDegrees, rightDegrees\n * @param {number} near Near bound of the frustum\n * @param {number} far Far bound of the frustum\n * @returns {mat4} out\n */\n\nexport function perspectiveFromFieldOfView(out, fov, near, far) {\n  var upTan = Math.tan(fov.upDegrees * Math.PI / 180.0);\n  var downTan = Math.tan(fov.downDegrees * Math.PI / 180.0);\n  var leftTan = Math.tan(fov.leftDegrees * Math.PI / 180.0);\n  var rightTan = Math.tan(fov.rightDegrees * Math.PI / 180.0);\n  var xScale = 2.0 / (leftTan + rightTan);\n  var yScale = 2.0 / (upTan + downTan);\n  out[0] = xScale;\n  out[1] = 0.0;\n  out[2] = 0.0;\n  out[3] = 0.0;\n  out[4] = 0.0;\n  out[5] = yScale;\n  out[6] = 0.0;\n  out[7] = 0.0;\n  out[8] = -((leftTan - rightTan) * xScale * 0.5);\n  out[9] = (upTan - downTan) * yScale * 0.5;\n  out[10] = far / (near - far);\n  out[11] = -1.0;\n  out[12] = 0.0;\n  out[13] = 0.0;\n  out[14] = far * near / (near - far);\n  out[15] = 0.0;\n  return out;\n}\n/**\n * Generates a orthogonal projection matrix with the given bounds.\n * The near/far clip planes correspond to a normalized device coordinate Z range of [-1, 1],\n * which matches WebGL/OpenGL's clip volume.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {number} left Left bound of the frustum\n * @param {number} right Right bound of the frustum\n * @param {number} bottom Bottom bound of the frustum\n * @param {number} top Top bound of the frustum\n * @param {number} near Near bound of the frustum\n * @param {number} far Far bound of the frustum\n * @returns {mat4} out\n */\n\nexport function orthoNO(out, left, right, bottom, top, near, far) {\n  var lr = 1 / (left - right);\n  var bt = 1 / (bottom - top);\n  var nf = 1 / (near - far);\n  out[0] = -2 * lr;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = -2 * bt;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = 2 * nf;\n  out[11] = 0;\n  out[12] = (left + right) * lr;\n  out[13] = (top + bottom) * bt;\n  out[14] = (far + near) * nf;\n  out[15] = 1;\n  return out;\n}\n/**\n * Alias for {@link mat4.orthoNO}\n * @function\n */\n\nexport var ortho = orthoNO;\n/**\n * Generates a orthogonal projection matrix with the given bounds.\n * The near/far clip planes correspond to a normalized device coordinate Z range of [0, 1],\n * which matches WebGPU/Vulkan/DirectX/Metal's clip volume.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {number} left Left bound of the frustum\n * @param {number} right Right bound of the frustum\n * @param {number} bottom Bottom bound of the frustum\n * @param {number} top Top bound of the frustum\n * @param {number} near Near bound of the frustum\n * @param {number} far Far bound of the frustum\n * @returns {mat4} out\n */\n\nexport function orthoZO(out, left, right, bottom, top, near, far) {\n  var lr = 1 / (left - right);\n  var bt = 1 / (bottom - top);\n  var nf = 1 / (near - far);\n  out[0] = -2 * lr;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 0;\n  out[4] = 0;\n  out[5] = -2 * bt;\n  out[6] = 0;\n  out[7] = 0;\n  out[8] = 0;\n  out[9] = 0;\n  out[10] = nf;\n  out[11] = 0;\n  out[12] = (left + right) * lr;\n  out[13] = (top + bottom) * bt;\n  out[14] = near * nf;\n  out[15] = 1;\n  return out;\n}\n/**\n * Generates a look-at matrix with the given eye position, focal point, and up axis.\n * If you want a matrix that actually makes an object look at another object, you should use targetTo instead.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {ReadonlyVec3} eye Position of the viewer\n * @param {ReadonlyVec3} center Point the viewer is looking at\n * @param {ReadonlyVec3} up vec3 pointing up\n * @returns {mat4} out\n */\n\nexport function lookAt(out, eye, center, up) {\n  var x0, x1, x2, y0, y1, y2, z0, z1, z2, len;\n  var eyex = eye[0];\n  var eyey = eye[1];\n  var eyez = eye[2];\n  var upx = up[0];\n  var upy = up[1];\n  var upz = up[2];\n  var centerx = center[0];\n  var centery = center[1];\n  var centerz = center[2];\n\n  if (Math.abs(eyex - centerx) < glMatrix.EPSILON && Math.abs(eyey - centery) < glMatrix.EPSILON && Math.abs(eyez - centerz) < glMatrix.EPSILON) {\n    return identity(out);\n  }\n\n  z0 = eyex - centerx;\n  z1 = eyey - centery;\n  z2 = eyez - centerz;\n  len = 1 / Math.hypot(z0, z1, z2);\n  z0 *= len;\n  z1 *= len;\n  z2 *= len;\n  x0 = upy * z2 - upz * z1;\n  x1 = upz * z0 - upx * z2;\n  x2 = upx * z1 - upy * z0;\n  len = Math.hypot(x0, x1, x2);\n\n  if (!len) {\n    x0 = 0;\n    x1 = 0;\n    x2 = 0;\n  } else {\n    len = 1 / len;\n    x0 *= len;\n    x1 *= len;\n    x2 *= len;\n  }\n\n  y0 = z1 * x2 - z2 * x1;\n  y1 = z2 * x0 - z0 * x2;\n  y2 = z0 * x1 - z1 * x0;\n  len = Math.hypot(y0, y1, y2);\n\n  if (!len) {\n    y0 = 0;\n    y1 = 0;\n    y2 = 0;\n  } else {\n    len = 1 / len;\n    y0 *= len;\n    y1 *= len;\n    y2 *= len;\n  }\n\n  out[0] = x0;\n  out[1] = y0;\n  out[2] = z0;\n  out[3] = 0;\n  out[4] = x1;\n  out[5] = y1;\n  out[6] = z1;\n  out[7] = 0;\n  out[8] = x2;\n  out[9] = y2;\n  out[10] = z2;\n  out[11] = 0;\n  out[12] = -(x0 * eyex + x1 * eyey + x2 * eyez);\n  out[13] = -(y0 * eyex + y1 * eyey + y2 * eyez);\n  out[14] = -(z0 * eyex + z1 * eyey + z2 * eyez);\n  out[15] = 1;\n  return out;\n}\n/**\n * Generates a matrix that makes something look at something else.\n *\n * @param {mat4} out mat4 frustum matrix will be written into\n * @param {ReadonlyVec3} eye Position of the viewer\n * @param {ReadonlyVec3} center Point the viewer is looking at\n * @param {ReadonlyVec3} up vec3 pointing up\n * @returns {mat4} out\n */\n\nexport function targetTo(out, eye, target, up) {\n  var eyex = eye[0],\n      eyey = eye[1],\n      eyez = eye[2],\n      upx = up[0],\n      upy = up[1],\n      upz = up[2];\n  var z0 = eyex - target[0],\n      z1 = eyey - target[1],\n      z2 = eyez - target[2];\n  var len = z0 * z0 + z1 * z1 + z2 * z2;\n\n  if (len > 0) {\n    len = 1 / Math.sqrt(len);\n    z0 *= len;\n    z1 *= len;\n    z2 *= len;\n  }\n\n  var x0 = upy * z2 - upz * z1,\n      x1 = upz * z0 - upx * z2,\n      x2 = upx * z1 - upy * z0;\n  len = x0 * x0 + x1 * x1 + x2 * x2;\n\n  if (len > 0) {\n    len = 1 / Math.sqrt(len);\n    x0 *= len;\n    x1 *= len;\n    x2 *= len;\n  }\n\n  out[0] = x0;\n  out[1] = x1;\n  out[2] = x2;\n  out[3] = 0;\n  out[4] = z1 * x2 - z2 * x1;\n  out[5] = z2 * x0 - z0 * x2;\n  out[6] = z0 * x1 - z1 * x0;\n  out[7] = 0;\n  out[8] = z0;\n  out[9] = z1;\n  out[10] = z2;\n  out[11] = 0;\n  out[12] = eyex;\n  out[13] = eyey;\n  out[14] = eyez;\n  out[15] = 1;\n  return out;\n}\n/**\n * Returns a string representation of a mat4\n *\n * @param {ReadonlyMat4} a matrix to represent as a string\n * @returns {String} string representation of the matrix\n */\n\nexport function str(a) {\n  return \"mat4(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \", \" + a[4] + \", \" + a[5] + \", \" + a[6] + \", \" + a[7] + \", \" + a[8] + \", \" + a[9] + \", \" + a[10] + \", \" + a[11] + \", \" + a[12] + \", \" + a[13] + \", \" + a[14] + \", \" + a[15] + \")\";\n}\n/**\n * Returns Frobenius norm of a mat4\n *\n * @param {ReadonlyMat4} a the matrix to calculate Frobenius norm of\n * @returns {Number} Frobenius norm\n */\n\nexport function frob(a) {\n  return Math.hypot(a[0], a[1], a[2], a[3], a[4], a[5], a[6], a[7], a[8], a[9], a[10], a[11], a[12], a[13], a[14], a[15]);\n}\n/**\n * Adds two mat4's\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the first operand\n * @param {ReadonlyMat4} b the second operand\n * @returns {mat4} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  out[4] = a[4] + b[4];\n  out[5] = a[5] + b[5];\n  out[6] = a[6] + b[6];\n  out[7] = a[7] + b[7];\n  out[8] = a[8] + b[8];\n  out[9] = a[9] + b[9];\n  out[10] = a[10] + b[10];\n  out[11] = a[11] + b[11];\n  out[12] = a[12] + b[12];\n  out[13] = a[13] + b[13];\n  out[14] = a[14] + b[14];\n  out[15] = a[15] + b[15];\n  return out;\n}\n/**\n * Subtracts matrix b from matrix a\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the first operand\n * @param {ReadonlyMat4} b the second operand\n * @returns {mat4} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  out[3] = a[3] - b[3];\n  out[4] = a[4] - b[4];\n  out[5] = a[5] - b[5];\n  out[6] = a[6] - b[6];\n  out[7] = a[7] - b[7];\n  out[8] = a[8] - b[8];\n  out[9] = a[9] - b[9];\n  out[10] = a[10] - b[10];\n  out[11] = a[11] - b[11];\n  out[12] = a[12] - b[12];\n  out[13] = a[13] - b[13];\n  out[14] = a[14] - b[14];\n  out[15] = a[15] - b[15];\n  return out;\n}\n/**\n * Multiply each element of the matrix by a scalar.\n *\n * @param {mat4} out the receiving matrix\n * @param {ReadonlyMat4} a the matrix to scale\n * @param {Number} b amount to scale the matrix's elements by\n * @returns {mat4} out\n */\n\nexport function multiplyScalar(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  out[4] = a[4] * b;\n  out[5] = a[5] * b;\n  out[6] = a[6] * b;\n  out[7] = a[7] * b;\n  out[8] = a[8] * b;\n  out[9] = a[9] * b;\n  out[10] = a[10] * b;\n  out[11] = a[11] * b;\n  out[12] = a[12] * b;\n  out[13] = a[13] * b;\n  out[14] = a[14] * b;\n  out[15] = a[15] * b;\n  return out;\n}\n/**\n * Adds two mat4's after multiplying each element of the second operand by a scalar value.\n *\n * @param {mat4} out the receiving vector\n * @param {ReadonlyMat4} a the first operand\n * @param {ReadonlyMat4} b the second operand\n * @param {Number} scale the amount to scale b's elements by before adding\n * @returns {mat4} out\n */\n\nexport function multiplyScalarAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  out[3] = a[3] + b[3] * scale;\n  out[4] = a[4] + b[4] * scale;\n  out[5] = a[5] + b[5] * scale;\n  out[6] = a[6] + b[6] * scale;\n  out[7] = a[7] + b[7] * scale;\n  out[8] = a[8] + b[8] * scale;\n  out[9] = a[9] + b[9] * scale;\n  out[10] = a[10] + b[10] * scale;\n  out[11] = a[11] + b[11] * scale;\n  out[12] = a[12] + b[12] * scale;\n  out[13] = a[13] + b[13] * scale;\n  out[14] = a[14] + b[14] * scale;\n  out[15] = a[15] + b[15] * scale;\n  return out;\n}\n/**\n * Returns whether or not the matrices have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyMat4} a The first matrix.\n * @param {ReadonlyMat4} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3] && a[4] === b[4] && a[5] === b[5] && a[6] === b[6] && a[7] === b[7] && a[8] === b[8] && a[9] === b[9] && a[10] === b[10] && a[11] === b[11] && a[12] === b[12] && a[13] === b[13] && a[14] === b[14] && a[15] === b[15];\n}\n/**\n * Returns whether or not the matrices have approximately the same elements in the same position.\n *\n * @param {ReadonlyMat4} a The first matrix.\n * @param {ReadonlyMat4} b The second matrix.\n * @returns {Boolean} True if the matrices are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var a4 = a[4],\n      a5 = a[5],\n      a6 = a[6],\n      a7 = a[7];\n  var a8 = a[8],\n      a9 = a[9],\n      a10 = a[10],\n      a11 = a[11];\n  var a12 = a[12],\n      a13 = a[13],\n      a14 = a[14],\n      a15 = a[15];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3];\n  var b4 = b[4],\n      b5 = b[5],\n      b6 = b[6],\n      b7 = b[7];\n  var b8 = b[8],\n      b9 = b[9],\n      b10 = b[10],\n      b11 = b[11];\n  var b12 = b[12],\n      b13 = b[13],\n      b14 = b[14],\n      b15 = b[15];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3)) && Math.abs(a4 - b4) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a4), Math.abs(b4)) && Math.abs(a5 - b5) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a5), Math.abs(b5)) && Math.abs(a6 - b6) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a6), Math.abs(b6)) && Math.abs(a7 - b7) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a7), Math.abs(b7)) && Math.abs(a8 - b8) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a8), Math.abs(b8)) && Math.abs(a9 - b9) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a9), Math.abs(b9)) && Math.abs(a10 - b10) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a10), Math.abs(b10)) && Math.abs(a11 - b11) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a11), Math.abs(b11)) && Math.abs(a12 - b12) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a12), Math.abs(b12)) && Math.abs(a13 - b13) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a13), Math.abs(b13)) && Math.abs(a14 - b14) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a14), Math.abs(b14)) && Math.abs(a15 - b15) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a15), Math.abs(b15));\n}\n/**\n * Alias for {@link mat4.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link mat4.subtract}\n * @function\n */\n\nexport var sub = subtract;", "import * as glMatrix from \"./common.js\";\nimport * as mat3 from \"./mat3.js\";\nimport * as vec3 from \"./vec3.js\";\nimport * as vec4 from \"./vec4.js\";\n/**\n * Quaternion\n * @module quat\n */\n\n/**\n * Creates a new identity quat\n *\n * @returns {quat} a new quaternion\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(4);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n    out[2] = 0;\n  }\n\n  out[3] = 1;\n  return out;\n}\n/**\n * Set a quat to the identity quaternion\n *\n * @param {quat} out the receiving quaternion\n * @returns {quat} out\n */\n\nexport function identity(out) {\n  out[0] = 0;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  return out;\n}\n/**\n * Sets a quat from the given angle and rotation axis,\n * then returns it.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyVec3} axis the axis around which to rotate\n * @param {Number} rad the angle in radians\n * @returns {quat} out\n **/\n\nexport function setAxisAngle(out, axis, rad) {\n  rad = rad * 0.5;\n  var s = Math.sin(rad);\n  out[0] = s * axis[0];\n  out[1] = s * axis[1];\n  out[2] = s * axis[2];\n  out[3] = Math.cos(rad);\n  return out;\n}\n/**\n * Gets the rotation axis and angle for a given\n *  quaternion. If a quaternion is created with\n *  setAxisAngle, this method will return the same\n *  values as providied in the original parameter list\n *  OR functionally equivalent values.\n * Example: The quaternion formed by axis [0, 0, 1] and\n *  angle -90 is the same as the quaternion formed by\n *  [0, 0, 1] and 270. This method favors the latter.\n * @param  {vec3} out_axis  Vector receiving the axis of rotation\n * @param  {ReadonlyQuat} q     Quaternion to be decomposed\n * @return {Number}     Angle, in radians, of the rotation\n */\n\nexport function getAxisAngle(out_axis, q) {\n  var rad = Math.acos(q[3]) * 2.0;\n  var s = Math.sin(rad / 2.0);\n\n  if (s > glMatrix.EPSILON) {\n    out_axis[0] = q[0] / s;\n    out_axis[1] = q[1] / s;\n    out_axis[2] = q[2] / s;\n  } else {\n    // If s is zero, return any axis (no rotation - axis does not matter)\n    out_axis[0] = 1;\n    out_axis[1] = 0;\n    out_axis[2] = 0;\n  }\n\n  return rad;\n}\n/**\n * Gets the angular distance between two unit quaternions\n *\n * @param  {ReadonlyQuat} a     Origin unit quaternion\n * @param  {ReadonlyQuat} b     Destination unit quaternion\n * @return {Number}     Angle, in radians, between the two quaternions\n */\n\nexport function getAngle(a, b) {\n  var dotproduct = dot(a, b);\n  return Math.acos(2 * dotproduct * dotproduct - 1);\n}\n/**\n * Multiplies two quat's\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @returns {quat} out\n */\n\nexport function multiply(out, a, b) {\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  var bx = b[0],\n      by = b[1],\n      bz = b[2],\n      bw = b[3];\n  out[0] = ax * bw + aw * bx + ay * bz - az * by;\n  out[1] = ay * bw + aw * by + az * bx - ax * bz;\n  out[2] = az * bw + aw * bz + ax * by - ay * bx;\n  out[3] = aw * bw - ax * bx - ay * by - az * bz;\n  return out;\n}\n/**\n * Rotates a quaternion by the given angle about the X axis\n *\n * @param {quat} out quat receiving operation result\n * @param {ReadonlyQuat} a quat to rotate\n * @param {number} rad angle (in radians) to rotate\n * @returns {quat} out\n */\n\nexport function rotateX(out, a, rad) {\n  rad *= 0.5;\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  var bx = Math.sin(rad),\n      bw = Math.cos(rad);\n  out[0] = ax * bw + aw * bx;\n  out[1] = ay * bw + az * bx;\n  out[2] = az * bw - ay * bx;\n  out[3] = aw * bw - ax * bx;\n  return out;\n}\n/**\n * Rotates a quaternion by the given angle about the Y axis\n *\n * @param {quat} out quat receiving operation result\n * @param {ReadonlyQuat} a quat to rotate\n * @param {number} rad angle (in radians) to rotate\n * @returns {quat} out\n */\n\nexport function rotateY(out, a, rad) {\n  rad *= 0.5;\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  var by = Math.sin(rad),\n      bw = Math.cos(rad);\n  out[0] = ax * bw - az * by;\n  out[1] = ay * bw + aw * by;\n  out[2] = az * bw + ax * by;\n  out[3] = aw * bw - ay * by;\n  return out;\n}\n/**\n * Rotates a quaternion by the given angle about the Z axis\n *\n * @param {quat} out quat receiving operation result\n * @param {ReadonlyQuat} a quat to rotate\n * @param {number} rad angle (in radians) to rotate\n * @returns {quat} out\n */\n\nexport function rotateZ(out, a, rad) {\n  rad *= 0.5;\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  var bz = Math.sin(rad),\n      bw = Math.cos(rad);\n  out[0] = ax * bw + ay * bz;\n  out[1] = ay * bw - ax * bz;\n  out[2] = az * bw + aw * bz;\n  out[3] = aw * bw - az * bz;\n  return out;\n}\n/**\n * Calculates the W component of a quat from the X, Y, and Z components.\n * Assumes that quaternion is 1 unit in length.\n * Any existing W component will be ignored.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate W component of\n * @returns {quat} out\n */\n\nexport function calculateW(out, a) {\n  var x = a[0],\n      y = a[1],\n      z = a[2];\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  out[3] = Math.sqrt(Math.abs(1.0 - x * x - y * y - z * z));\n  return out;\n}\n/**\n * Calculate the exponential of a unit quaternion.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate the exponential of\n * @returns {quat} out\n */\n\nexport function exp(out, a) {\n  var x = a[0],\n      y = a[1],\n      z = a[2],\n      w = a[3];\n  var r = Math.sqrt(x * x + y * y + z * z);\n  var et = Math.exp(w);\n  var s = r > 0 ? et * Math.sin(r) / r : 0;\n  out[0] = x * s;\n  out[1] = y * s;\n  out[2] = z * s;\n  out[3] = et * Math.cos(r);\n  return out;\n}\n/**\n * Calculate the natural logarithm of a unit quaternion.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate the exponential of\n * @returns {quat} out\n */\n\nexport function ln(out, a) {\n  var x = a[0],\n      y = a[1],\n      z = a[2],\n      w = a[3];\n  var r = Math.sqrt(x * x + y * y + z * z);\n  var t = r > 0 ? Math.atan2(r, w) / r : 0;\n  out[0] = x * t;\n  out[1] = y * t;\n  out[2] = z * t;\n  out[3] = 0.5 * Math.log(x * x + y * y + z * z + w * w);\n  return out;\n}\n/**\n * Calculate the scalar power of a unit quaternion.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate the exponential of\n * @param {Number} b amount to scale the quaternion by\n * @returns {quat} out\n */\n\nexport function pow(out, a, b) {\n  ln(out, a);\n  scale(out, out, b);\n  exp(out, out);\n  return out;\n}\n/**\n * Performs a spherical linear interpolation between two quat\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {quat} out\n */\n\nexport function slerp(out, a, b, t) {\n  // benchmarks:\n  //    http://jsperf.com/quaternion-slerp-implementations\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  var bx = b[0],\n      by = b[1],\n      bz = b[2],\n      bw = b[3];\n  var omega, cosom, sinom, scale0, scale1; // calc cosine\n\n  cosom = ax * bx + ay * by + az * bz + aw * bw; // adjust signs (if necessary)\n\n  if (cosom < 0.0) {\n    cosom = -cosom;\n    bx = -bx;\n    by = -by;\n    bz = -bz;\n    bw = -bw;\n  } // calculate coefficients\n\n\n  if (1.0 - cosom > glMatrix.EPSILON) {\n    // standard case (slerp)\n    omega = Math.acos(cosom);\n    sinom = Math.sin(omega);\n    scale0 = Math.sin((1.0 - t) * omega) / sinom;\n    scale1 = Math.sin(t * omega) / sinom;\n  } else {\n    // \"from\" and \"to\" quaternions are very close\n    //  ... so we can do a linear interpolation\n    scale0 = 1.0 - t;\n    scale1 = t;\n  } // calculate final values\n\n\n  out[0] = scale0 * ax + scale1 * bx;\n  out[1] = scale0 * ay + scale1 * by;\n  out[2] = scale0 * az + scale1 * bz;\n  out[3] = scale0 * aw + scale1 * bw;\n  return out;\n}\n/**\n * Generates a random unit quaternion\n *\n * @param {quat} out the receiving quaternion\n * @returns {quat} out\n */\n\nexport function random(out) {\n  // Implementation of http://planning.cs.uiuc.edu/node198.html\n  // TODO: Calling random 3 times is probably not the fastest solution\n  var u1 = glMatrix.RANDOM();\n  var u2 = glMatrix.RANDOM();\n  var u3 = glMatrix.RANDOM();\n  var sqrt1MinusU1 = Math.sqrt(1 - u1);\n  var sqrtU1 = Math.sqrt(u1);\n  out[0] = sqrt1MinusU1 * Math.sin(2.0 * Math.PI * u2);\n  out[1] = sqrt1MinusU1 * Math.cos(2.0 * Math.PI * u2);\n  out[2] = sqrtU1 * Math.sin(2.0 * Math.PI * u3);\n  out[3] = sqrtU1 * Math.cos(2.0 * Math.PI * u3);\n  return out;\n}\n/**\n * Calculates the inverse of a quat\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate inverse of\n * @returns {quat} out\n */\n\nexport function invert(out, a) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var dot = a0 * a0 + a1 * a1 + a2 * a2 + a3 * a3;\n  var invDot = dot ? 1.0 / dot : 0; // TODO: Would be faster to return [0,0,0,0] immediately if dot == 0\n\n  out[0] = -a0 * invDot;\n  out[1] = -a1 * invDot;\n  out[2] = -a2 * invDot;\n  out[3] = a3 * invDot;\n  return out;\n}\n/**\n * Calculates the conjugate of a quat\n * If the quaternion is normalized, this function is faster than quat.inverse and produces the same result.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quat to calculate conjugate of\n * @returns {quat} out\n */\n\nexport function conjugate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  out[3] = a[3];\n  return out;\n}\n/**\n * Creates a quaternion from the given 3x3 rotation matrix.\n *\n * NOTE: The resultant quaternion is not normalized, so you should be sure\n * to renormalize the quaternion yourself where necessary.\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyMat3} m rotation matrix\n * @returns {quat} out\n * @function\n */\n\nexport function fromMat3(out, m) {\n  // Algorithm in Ken Shoemake's article in 1987 SIGGRAPH course notes\n  // article \"Quaternion Calculus and Fast Animation\".\n  var fTrace = m[0] + m[4] + m[8];\n  var fRoot;\n\n  if (fTrace > 0.0) {\n    // |w| > 1/2, may as well choose w > 1/2\n    fRoot = Math.sqrt(fTrace + 1.0); // 2w\n\n    out[3] = 0.5 * fRoot;\n    fRoot = 0.5 / fRoot; // 1/(4w)\n\n    out[0] = (m[5] - m[7]) * fRoot;\n    out[1] = (m[6] - m[2]) * fRoot;\n    out[2] = (m[1] - m[3]) * fRoot;\n  } else {\n    // |w| <= 1/2\n    var i = 0;\n    if (m[4] > m[0]) i = 1;\n    if (m[8] > m[i * 3 + i]) i = 2;\n    var j = (i + 1) % 3;\n    var k = (i + 2) % 3;\n    fRoot = Math.sqrt(m[i * 3 + i] - m[j * 3 + j] - m[k * 3 + k] + 1.0);\n    out[i] = 0.5 * fRoot;\n    fRoot = 0.5 / fRoot;\n    out[3] = (m[j * 3 + k] - m[k * 3 + j]) * fRoot;\n    out[j] = (m[j * 3 + i] + m[i * 3 + j]) * fRoot;\n    out[k] = (m[k * 3 + i] + m[i * 3 + k]) * fRoot;\n  }\n\n  return out;\n}\n/**\n * Creates a quaternion from the given euler angle x, y, z.\n *\n * @param {quat} out the receiving quaternion\n * @param {x} Angle to rotate around X axis in degrees.\n * @param {y} Angle to rotate around Y axis in degrees.\n * @param {z} Angle to rotate around Z axis in degrees.\n * @returns {quat} out\n * @function\n */\n\nexport function fromEuler(out, x, y, z) {\n  var halfToRad = 0.5 * Math.PI / 180.0;\n  x *= halfToRad;\n  y *= halfToRad;\n  z *= halfToRad;\n  var sx = Math.sin(x);\n  var cx = Math.cos(x);\n  var sy = Math.sin(y);\n  var cy = Math.cos(y);\n  var sz = Math.sin(z);\n  var cz = Math.cos(z);\n  out[0] = sx * cy * cz - cx * sy * sz;\n  out[1] = cx * sy * cz + sx * cy * sz;\n  out[2] = cx * cy * sz - sx * sy * cz;\n  out[3] = cx * cy * cz + sx * sy * sz;\n  return out;\n}\n/**\n * Returns a string representation of a quatenion\n *\n * @param {ReadonlyQuat} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\n\nexport function str(a) {\n  return \"quat(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \")\";\n}\n/**\n * Creates a new quat initialized with values from an existing quaternion\n *\n * @param {ReadonlyQuat} a quaternion to clone\n * @returns {quat} a new quaternion\n * @function\n */\n\nexport var clone = vec4.clone;\n/**\n * Creates a new quat initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @param {Number} w W component\n * @returns {quat} a new quaternion\n * @function\n */\n\nexport var fromValues = vec4.fromValues;\n/**\n * Copy the values from one quat to another\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the source quaternion\n * @returns {quat} out\n * @function\n */\n\nexport var copy = vec4.copy;\n/**\n * Set the components of a quat to the given values\n *\n * @param {quat} out the receiving quaternion\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @param {Number} w W component\n * @returns {quat} out\n * @function\n */\n\nexport var set = vec4.set;\n/**\n * Adds two quat's\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @returns {quat} out\n * @function\n */\n\nexport var add = vec4.add;\n/**\n * Alias for {@link quat.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Scales a quat by a scalar number\n *\n * @param {quat} out the receiving vector\n * @param {ReadonlyQuat} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {quat} out\n * @function\n */\n\nexport var scale = vec4.scale;\n/**\n * Calculates the dot product of two quat's\n *\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @returns {Number} dot product of a and b\n * @function\n */\n\nexport var dot = vec4.dot;\n/**\n * Performs a linear interpolation between two quat's\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {quat} out\n * @function\n */\n\nexport var lerp = vec4.lerp;\n/**\n * Calculates the length of a quat\n *\n * @param {ReadonlyQuat} a vector to calculate length of\n * @returns {Number} length of a\n */\n\nexport var length = vec4.length;\n/**\n * Alias for {@link quat.length}\n * @function\n */\n\nexport var len = length;\n/**\n * Calculates the squared length of a quat\n *\n * @param {ReadonlyQuat} a vector to calculate squared length of\n * @returns {Number} squared length of a\n * @function\n */\n\nexport var squaredLength = vec4.squaredLength;\n/**\n * Alias for {@link quat.squaredLength}\n * @function\n */\n\nexport var sqrLen = squaredLength;\n/**\n * Normalize a quat\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a quaternion to normalize\n * @returns {quat} out\n * @function\n */\n\nexport var normalize = vec4.normalize;\n/**\n * Returns whether or not the quaternions have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyQuat} a The first quaternion.\n * @param {ReadonlyQuat} b The second quaternion.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport var exactEquals = vec4.exactEquals;\n/**\n * Returns whether or not the quaternions have approximately the same elements in the same position.\n *\n * @param {ReadonlyQuat} a The first vector.\n * @param {ReadonlyQuat} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport var equals = vec4.equals;\n/**\n * Sets a quaternion to represent the shortest rotation from one\n * vector to another.\n *\n * Both vectors are assumed to be unit length.\n *\n * @param {quat} out the receiving quaternion.\n * @param {ReadonlyVec3} a the initial vector\n * @param {ReadonlyVec3} b the destination vector\n * @returns {quat} out\n */\n\nexport var rotationTo = function () {\n  var tmpvec3 = vec3.create();\n  var xUnitVec3 = vec3.fromValues(1, 0, 0);\n  var yUnitVec3 = vec3.fromValues(0, 1, 0);\n  return function (out, a, b) {\n    var dot = vec3.dot(a, b);\n\n    if (dot < -0.999999) {\n      vec3.cross(tmpvec3, xUnitVec3, a);\n      if (vec3.len(tmpvec3) < 0.000001) vec3.cross(tmpvec3, yUnitVec3, a);\n      vec3.normalize(tmpvec3, tmpvec3);\n      setAxisAngle(out, tmpvec3, Math.PI);\n      return out;\n    } else if (dot > 0.999999) {\n      out[0] = 0;\n      out[1] = 0;\n      out[2] = 0;\n      out[3] = 1;\n      return out;\n    } else {\n      vec3.cross(tmpvec3, a, b);\n      out[0] = tmpvec3[0];\n      out[1] = tmpvec3[1];\n      out[2] = tmpvec3[2];\n      out[3] = 1 + dot;\n      return normalize(out, out);\n    }\n  };\n}();\n/**\n * Performs a spherical linear interpolation with two control points\n *\n * @param {quat} out the receiving quaternion\n * @param {ReadonlyQuat} a the first operand\n * @param {ReadonlyQuat} b the second operand\n * @param {ReadonlyQuat} c the third operand\n * @param {ReadonlyQuat} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {quat} out\n */\n\nexport var sqlerp = function () {\n  var temp1 = create();\n  var temp2 = create();\n  return function (out, a, b, c, d, t) {\n    slerp(temp1, a, d, t);\n    slerp(temp2, b, c, t);\n    slerp(out, temp1, temp2, 2 * t * (1 - t));\n    return out;\n  };\n}();\n/**\n * Sets the specified quaternion with values corresponding to the given\n * axes. Each axis is a vec3 and is expected to be unit length and\n * perpendicular to all other specified axes.\n *\n * @param {ReadonlyVec3} view  the vector representing the viewing direction\n * @param {ReadonlyVec3} right the vector representing the local \"right\" direction\n * @param {ReadonlyVec3} up    the vector representing the local \"up\" direction\n * @returns {quat} out\n */\n\nexport var setAxes = function () {\n  var matr = mat3.create();\n  return function (out, view, right, up) {\n    matr[0] = right[0];\n    matr[3] = right[1];\n    matr[6] = right[2];\n    matr[1] = up[0];\n    matr[4] = up[1];\n    matr[7] = up[2];\n    matr[2] = -view[0];\n    matr[5] = -view[1];\n    matr[8] = -view[2];\n    return normalize(out, fromMat3(out, matr));\n  };\n}();", "import * as glMatrix from \"./common.js\";\n/**\n * 3 Dimensional Vector\n * @module vec3\n */\n\n/**\n * Creates a new, empty vec3\n *\n * @returns {vec3} a new 3D vector\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(3);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n    out[2] = 0;\n  }\n\n  return out;\n}\n/**\n * Creates a new vec3 initialized with values from an existing vector\n *\n * @param {ReadonlyVec3} a vector to clone\n * @returns {vec3} a new 3D vector\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n/**\n * Calculates the length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate length of\n * @returns {Number} length of a\n */\n\nexport function length(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return Math.hypot(x, y, z);\n}\n/**\n * Creates a new vec3 initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} a new 3D vector\n */\n\nexport function fromValues(x, y, z) {\n  var out = new glMatrix.ARRAY_TYPE(3);\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n/**\n * Copy the values from one vec3 to another\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the source vector\n * @returns {vec3} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  return out;\n}\n/**\n * Set the components of a vec3 to the given values\n *\n * @param {vec3} out the receiving vector\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @returns {vec3} out\n */\n\nexport function set(out, x, y, z) {\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  return out;\n}\n/**\n * Adds two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  return out;\n}\n/**\n * Subtracts vector b from vector a\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  return out;\n}\n/**\n * Multiplies two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function multiply(out, a, b) {\n  out[0] = a[0] * b[0];\n  out[1] = a[1] * b[1];\n  out[2] = a[2] * b[2];\n  return out;\n}\n/**\n * Divides two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function divide(out, a, b) {\n  out[0] = a[0] / b[0];\n  out[1] = a[1] / b[1];\n  out[2] = a[2] / b[2];\n  return out;\n}\n/**\n * Math.ceil the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to ceil\n * @returns {vec3} out\n */\n\nexport function ceil(out, a) {\n  out[0] = Math.ceil(a[0]);\n  out[1] = Math.ceil(a[1]);\n  out[2] = Math.ceil(a[2]);\n  return out;\n}\n/**\n * Math.floor the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to floor\n * @returns {vec3} out\n */\n\nexport function floor(out, a) {\n  out[0] = Math.floor(a[0]);\n  out[1] = Math.floor(a[1]);\n  out[2] = Math.floor(a[2]);\n  return out;\n}\n/**\n * Returns the minimum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function min(out, a, b) {\n  out[0] = Math.min(a[0], b[0]);\n  out[1] = Math.min(a[1], b[1]);\n  out[2] = Math.min(a[2], b[2]);\n  return out;\n}\n/**\n * Returns the maximum of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function max(out, a, b) {\n  out[0] = Math.max(a[0], b[0]);\n  out[1] = Math.max(a[1], b[1]);\n  out[2] = Math.max(a[2], b[2]);\n  return out;\n}\n/**\n * Math.round the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to round\n * @returns {vec3} out\n */\n\nexport function round(out, a) {\n  out[0] = Math.round(a[0]);\n  out[1] = Math.round(a[1]);\n  out[2] = Math.round(a[2]);\n  return out;\n}\n/**\n * Scales a vec3 by a scalar number\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {vec3} out\n */\n\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  return out;\n}\n/**\n * Adds two vec3's after scaling the second operand by a scalar value\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} scale the amount to scale b by before adding\n * @returns {vec3} out\n */\n\nexport function scaleAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  return out;\n}\n/**\n * Calculates the euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} distance between a and b\n */\n\nexport function distance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return Math.hypot(x, y, z);\n}\n/**\n * Calculates the squared euclidian distance between two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} squared distance between a and b\n */\n\nexport function squaredDistance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  return x * x + y * y + z * z;\n}\n/**\n * Calculates the squared length of a vec3\n *\n * @param {ReadonlyVec3} a vector to calculate squared length of\n * @returns {Number} squared length of a\n */\n\nexport function squaredLength(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  return x * x + y * y + z * z;\n}\n/**\n * Negates the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to negate\n * @returns {vec3} out\n */\n\nexport function negate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  return out;\n}\n/**\n * Returns the inverse of the components of a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to invert\n * @returns {vec3} out\n */\n\nexport function inverse(out, a) {\n  out[0] = 1.0 / a[0];\n  out[1] = 1.0 / a[1];\n  out[2] = 1.0 / a[2];\n  return out;\n}\n/**\n * Normalize a vec3\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a vector to normalize\n * @returns {vec3} out\n */\n\nexport function normalize(out, a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var len = x * x + y * y + z * z;\n\n  if (len > 0) {\n    //TODO: evaluate use of glm_invsqrt here?\n    len = 1 / Math.sqrt(len);\n  }\n\n  out[0] = a[0] * len;\n  out[1] = a[1] * len;\n  out[2] = a[2] * len;\n  return out;\n}\n/**\n * Calculates the dot product of two vec3's\n *\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {Number} dot product of a and b\n */\n\nexport function dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n/**\n * Computes the cross product of two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @returns {vec3} out\n */\n\nexport function cross(out, a, b) {\n  var ax = a[0],\n      ay = a[1],\n      az = a[2];\n  var bx = b[0],\n      by = b[1],\n      bz = b[2];\n  out[0] = ay * bz - az * by;\n  out[1] = az * bx - ax * bz;\n  out[2] = ax * by - ay * bx;\n  return out;\n}\n/**\n * Performs a linear interpolation between two vec3's\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\n\nexport function lerp(out, a, b, t) {\n  var ax = a[0];\n  var ay = a[1];\n  var az = a[2];\n  out[0] = ax + t * (b[0] - ax);\n  out[1] = ay + t * (b[1] - ay);\n  out[2] = az + t * (b[2] - az);\n  return out;\n}\n/**\n * Performs a hermite interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\n\nexport function hermite(out, a, b, c, d, t) {\n  var factorTimes2 = t * t;\n  var factor1 = factorTimes2 * (2 * t - 3) + 1;\n  var factor2 = factorTimes2 * (t - 2) + t;\n  var factor3 = factorTimes2 * (t - 1);\n  var factor4 = factorTimes2 * (3 - 2 * t);\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n/**\n * Performs a bezier interpolation with two control points\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the first operand\n * @param {ReadonlyVec3} b the second operand\n * @param {ReadonlyVec3} c the third operand\n * @param {ReadonlyVec3} d the fourth operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec3} out\n */\n\nexport function bezier(out, a, b, c, d, t) {\n  var inverseFactor = 1 - t;\n  var inverseFactorTimesTwo = inverseFactor * inverseFactor;\n  var factorTimes2 = t * t;\n  var factor1 = inverseFactorTimesTwo * inverseFactor;\n  var factor2 = 3 * t * inverseFactorTimesTwo;\n  var factor3 = 3 * factorTimes2 * inverseFactor;\n  var factor4 = factorTimes2 * t;\n  out[0] = a[0] * factor1 + b[0] * factor2 + c[0] * factor3 + d[0] * factor4;\n  out[1] = a[1] * factor1 + b[1] * factor2 + c[1] * factor3 + d[1] * factor4;\n  out[2] = a[2] * factor1 + b[2] * factor2 + c[2] * factor3 + d[2] * factor4;\n  return out;\n}\n/**\n * Generates a random vector with the given scale\n *\n * @param {vec3} out the receiving vector\n * @param {Number} [scale] Length of the resulting vector. If ommitted, a unit vector will be returned\n * @returns {vec3} out\n */\n\nexport function random(out, scale) {\n  scale = scale || 1.0;\n  var r = glMatrix.RANDOM() * 2.0 * Math.PI;\n  var z = glMatrix.RANDOM() * 2.0 - 1.0;\n  var zScale = Math.sqrt(1.0 - z * z) * scale;\n  out[0] = Math.cos(r) * zScale;\n  out[1] = Math.sin(r) * zScale;\n  out[2] = z * scale;\n  return out;\n}\n/**\n * Transforms the vec3 with a mat4.\n * 4th vector component is implicitly '1'\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat4} m matrix to transform with\n * @returns {vec3} out\n */\n\nexport function transformMat4(out, a, m) {\n  var x = a[0],\n      y = a[1],\n      z = a[2];\n  var w = m[3] * x + m[7] * y + m[11] * z + m[15];\n  w = w || 1.0;\n  out[0] = (m[0] * x + m[4] * y + m[8] * z + m[12]) / w;\n  out[1] = (m[1] * x + m[5] * y + m[9] * z + m[13]) / w;\n  out[2] = (m[2] * x + m[6] * y + m[10] * z + m[14]) / w;\n  return out;\n}\n/**\n * Transforms the vec3 with a mat3.\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyMat3} m the 3x3 matrix to transform with\n * @returns {vec3} out\n */\n\nexport function transformMat3(out, a, m) {\n  var x = a[0],\n      y = a[1],\n      z = a[2];\n  out[0] = x * m[0] + y * m[3] + z * m[6];\n  out[1] = x * m[1] + y * m[4] + z * m[7];\n  out[2] = x * m[2] + y * m[5] + z * m[8];\n  return out;\n}\n/**\n * Transforms the vec3 with a quat\n * Can also be used for dual quaternions. (Multiply it with the real part)\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec3} a the vector to transform\n * @param {ReadonlyQuat} q quaternion to transform with\n * @returns {vec3} out\n */\n\nexport function transformQuat(out, a, q) {\n  // benchmarks: https://jsperf.com/quaternion-transform-vec3-implementations-fixed\n  var qx = q[0],\n      qy = q[1],\n      qz = q[2],\n      qw = q[3];\n  var x = a[0],\n      y = a[1],\n      z = a[2]; // var qvec = [qx, qy, qz];\n  // var uv = vec3.cross([], qvec, a);\n\n  var uvx = qy * z - qz * y,\n      uvy = qz * x - qx * z,\n      uvz = qx * y - qy * x; // var uuv = vec3.cross([], qvec, uv);\n\n  var uuvx = qy * uvz - qz * uvy,\n      uuvy = qz * uvx - qx * uvz,\n      uuvz = qx * uvy - qy * uvx; // vec3.scale(uv, uv, 2 * w);\n\n  var w2 = qw * 2;\n  uvx *= w2;\n  uvy *= w2;\n  uvz *= w2; // vec3.scale(uuv, uuv, 2);\n\n  uuvx *= 2;\n  uuvy *= 2;\n  uuvz *= 2; // return vec3.add(out, a, vec3.add(out, uv, uuv));\n\n  out[0] = x + uvx + uuvx;\n  out[1] = y + uvy + uuvy;\n  out[2] = z + uvz + uuvz;\n  return out;\n}\n/**\n * Rotate a 3D vector around the x-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\n\nexport function rotateX(out, a, b, rad) {\n  var p = [],\n      r = []; //Translate point to the origin\n\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2]; //perform rotation\n\n  r[0] = p[0];\n  r[1] = p[1] * Math.cos(rad) - p[2] * Math.sin(rad);\n  r[2] = p[1] * Math.sin(rad) + p[2] * Math.cos(rad); //translate to correct position\n\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n/**\n * Rotate a 3D vector around the y-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\n\nexport function rotateY(out, a, b, rad) {\n  var p = [],\n      r = []; //Translate point to the origin\n\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2]; //perform rotation\n\n  r[0] = p[2] * Math.sin(rad) + p[0] * Math.cos(rad);\n  r[1] = p[1];\n  r[2] = p[2] * Math.cos(rad) - p[0] * Math.sin(rad); //translate to correct position\n\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n/**\n * Rotate a 3D vector around the z-axis\n * @param {vec3} out The receiving vec3\n * @param {ReadonlyVec3} a The vec3 point to rotate\n * @param {ReadonlyVec3} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec3} out\n */\n\nexport function rotateZ(out, a, b, rad) {\n  var p = [],\n      r = []; //Translate point to the origin\n\n  p[0] = a[0] - b[0];\n  p[1] = a[1] - b[1];\n  p[2] = a[2] - b[2]; //perform rotation\n\n  r[0] = p[0] * Math.cos(rad) - p[1] * Math.sin(rad);\n  r[1] = p[0] * Math.sin(rad) + p[1] * Math.cos(rad);\n  r[2] = p[2]; //translate to correct position\n\n  out[0] = r[0] + b[0];\n  out[1] = r[1] + b[1];\n  out[2] = r[2] + b[2];\n  return out;\n}\n/**\n * Get the angle between two 3D vectors\n * @param {ReadonlyVec3} a The first operand\n * @param {ReadonlyVec3} b The second operand\n * @returns {Number} The angle in radians\n */\n\nexport function angle(a, b) {\n  var ax = a[0],\n      ay = a[1],\n      az = a[2],\n      bx = b[0],\n      by = b[1],\n      bz = b[2],\n      mag1 = Math.sqrt(ax * ax + ay * ay + az * az),\n      mag2 = Math.sqrt(bx * bx + by * by + bz * bz),\n      mag = mag1 * mag2,\n      cosine = mag && dot(a, b) / mag;\n  return Math.acos(Math.min(Math.max(cosine, -1), 1));\n}\n/**\n * Set the components of a vec3 to zero\n *\n * @param {vec3} out the receiving vector\n * @returns {vec3} out\n */\n\nexport function zero(out) {\n  out[0] = 0.0;\n  out[1] = 0.0;\n  out[2] = 0.0;\n  return out;\n}\n/**\n * Returns a string representation of a vector\n *\n * @param {ReadonlyVec3} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\n\nexport function str(a) {\n  return \"vec3(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \")\";\n}\n/**\n * Returns whether or not the vectors have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2];\n}\n/**\n * Returns whether or not the vectors have approximately the same elements in the same position.\n *\n * @param {ReadonlyVec3} a The first vector.\n * @param {ReadonlyVec3} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2));\n}\n/**\n * Alias for {@link vec3.subtract}\n * @function\n */\n\nexport var sub = subtract;\n/**\n * Alias for {@link vec3.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link vec3.divide}\n * @function\n */\n\nexport var div = divide;\n/**\n * Alias for {@link vec3.distance}\n * @function\n */\n\nexport var dist = distance;\n/**\n * Alias for {@link vec3.squaredDistance}\n * @function\n */\n\nexport var sqrDist = squaredDistance;\n/**\n * Alias for {@link vec3.length}\n * @function\n */\n\nexport var len = length;\n/**\n * Alias for {@link vec3.squaredLength}\n * @function\n */\n\nexport var sqrLen = squaredLength;\n/**\n * Perform some operation over an array of vec3s.\n *\n * @param {Array} a the array of vectors to iterate over\n * @param {Number} stride Number of elements between the start of each vec3. If 0 assumes tightly packed\n * @param {Number} offset Number of elements to skip at the beginning of the array\n * @param {Number} count Number of vec3s to iterate over. If 0 iterates over entire array\n * @param {Function} fn Function to call for each vector in the array\n * @param {Object} [arg] additional argument to pass to fn\n * @returns {Array} a\n * @function\n */\n\nexport var forEach = function () {\n  var vec = create();\n  return function (a, stride, offset, count, fn, arg) {\n    var i, l;\n\n    if (!stride) {\n      stride = 3;\n    }\n\n    if (!offset) {\n      offset = 0;\n    }\n\n    if (count) {\n      l = Math.min(count * stride + offset, a.length);\n    } else {\n      l = a.length;\n    }\n\n    for (i = offset; i < l; i += stride) {\n      vec[0] = a[i];\n      vec[1] = a[i + 1];\n      vec[2] = a[i + 2];\n      fn(vec, vec, arg);\n      a[i] = vec[0];\n      a[i + 1] = vec[1];\n      a[i + 2] = vec[2];\n    }\n\n    return a;\n  };\n}();", "import * as glMatrix from \"./common.js\";\n/**\n * 4 Dimensional Vector\n * @module vec4\n */\n\n/**\n * Creates a new, empty vec4\n *\n * @returns {vec4} a new 4D vector\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(4);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n    out[2] = 0;\n    out[3] = 0;\n  }\n\n  return out;\n}\n/**\n * Creates a new vec4 initialized with values from an existing vector\n *\n * @param {ReadonlyVec4} a vector to clone\n * @returns {vec4} a new 4D vector\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(4);\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\n/**\n * Creates a new vec4 initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @param {Number} w W component\n * @returns {vec4} a new 4D vector\n */\n\nexport function fromValues(x, y, z, w) {\n  var out = new glMatrix.ARRAY_TYPE(4);\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  out[3] = w;\n  return out;\n}\n/**\n * Copy the values from one vec4 to another\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the source vector\n * @returns {vec4} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  return out;\n}\n/**\n * Set the components of a vec4 to the given values\n *\n * @param {vec4} out the receiving vector\n * @param {Number} x X component\n * @param {Number} y Y component\n * @param {Number} z Z component\n * @param {Number} w W component\n * @returns {vec4} out\n */\n\nexport function set(out, x, y, z, w) {\n  out[0] = x;\n  out[1] = y;\n  out[2] = z;\n  out[3] = w;\n  return out;\n}\n/**\n * Adds two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  return out;\n}\n/**\n * Subtracts vector b from vector a\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  out[2] = a[2] - b[2];\n  out[3] = a[3] - b[3];\n  return out;\n}\n/**\n * Multiplies two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function multiply(out, a, b) {\n  out[0] = a[0] * b[0];\n  out[1] = a[1] * b[1];\n  out[2] = a[2] * b[2];\n  out[3] = a[3] * b[3];\n  return out;\n}\n/**\n * Divides two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function divide(out, a, b) {\n  out[0] = a[0] / b[0];\n  out[1] = a[1] / b[1];\n  out[2] = a[2] / b[2];\n  out[3] = a[3] / b[3];\n  return out;\n}\n/**\n * Math.ceil the components of a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to ceil\n * @returns {vec4} out\n */\n\nexport function ceil(out, a) {\n  out[0] = Math.ceil(a[0]);\n  out[1] = Math.ceil(a[1]);\n  out[2] = Math.ceil(a[2]);\n  out[3] = Math.ceil(a[3]);\n  return out;\n}\n/**\n * Math.floor the components of a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to floor\n * @returns {vec4} out\n */\n\nexport function floor(out, a) {\n  out[0] = Math.floor(a[0]);\n  out[1] = Math.floor(a[1]);\n  out[2] = Math.floor(a[2]);\n  out[3] = Math.floor(a[3]);\n  return out;\n}\n/**\n * Returns the minimum of two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function min(out, a, b) {\n  out[0] = Math.min(a[0], b[0]);\n  out[1] = Math.min(a[1], b[1]);\n  out[2] = Math.min(a[2], b[2]);\n  out[3] = Math.min(a[3], b[3]);\n  return out;\n}\n/**\n * Returns the maximum of two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {vec4} out\n */\n\nexport function max(out, a, b) {\n  out[0] = Math.max(a[0], b[0]);\n  out[1] = Math.max(a[1], b[1]);\n  out[2] = Math.max(a[2], b[2]);\n  out[3] = Math.max(a[3], b[3]);\n  return out;\n}\n/**\n * Math.round the components of a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to round\n * @returns {vec4} out\n */\n\nexport function round(out, a) {\n  out[0] = Math.round(a[0]);\n  out[1] = Math.round(a[1]);\n  out[2] = Math.round(a[2]);\n  out[3] = Math.round(a[3]);\n  return out;\n}\n/**\n * Scales a vec4 by a scalar number\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {vec4} out\n */\n\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  return out;\n}\n/**\n * Adds two vec4's after scaling the second operand by a scalar value\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @param {Number} scale the amount to scale b by before adding\n * @returns {vec4} out\n */\n\nexport function scaleAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  out[2] = a[2] + b[2] * scale;\n  out[3] = a[3] + b[3] * scale;\n  return out;\n}\n/**\n * Calculates the euclidian distance between two vec4's\n *\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {Number} distance between a and b\n */\n\nexport function distance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  var w = b[3] - a[3];\n  return Math.hypot(x, y, z, w);\n}\n/**\n * Calculates the squared euclidian distance between two vec4's\n *\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {Number} squared distance between a and b\n */\n\nexport function squaredDistance(a, b) {\n  var x = b[0] - a[0];\n  var y = b[1] - a[1];\n  var z = b[2] - a[2];\n  var w = b[3] - a[3];\n  return x * x + y * y + z * z + w * w;\n}\n/**\n * Calculates the length of a vec4\n *\n * @param {ReadonlyVec4} a vector to calculate length of\n * @returns {Number} length of a\n */\n\nexport function length(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var w = a[3];\n  return Math.hypot(x, y, z, w);\n}\n/**\n * Calculates the squared length of a vec4\n *\n * @param {ReadonlyVec4} a vector to calculate squared length of\n * @returns {Number} squared length of a\n */\n\nexport function squaredLength(a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var w = a[3];\n  return x * x + y * y + z * z + w * w;\n}\n/**\n * Negates the components of a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to negate\n * @returns {vec4} out\n */\n\nexport function negate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  out[3] = -a[3];\n  return out;\n}\n/**\n * Returns the inverse of the components of a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to invert\n * @returns {vec4} out\n */\n\nexport function inverse(out, a) {\n  out[0] = 1.0 / a[0];\n  out[1] = 1.0 / a[1];\n  out[2] = 1.0 / a[2];\n  out[3] = 1.0 / a[3];\n  return out;\n}\n/**\n * Normalize a vec4\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a vector to normalize\n * @returns {vec4} out\n */\n\nexport function normalize(out, a) {\n  var x = a[0];\n  var y = a[1];\n  var z = a[2];\n  var w = a[3];\n  var len = x * x + y * y + z * z + w * w;\n\n  if (len > 0) {\n    len = 1 / Math.sqrt(len);\n  }\n\n  out[0] = x * len;\n  out[1] = y * len;\n  out[2] = z * len;\n  out[3] = w * len;\n  return out;\n}\n/**\n * Calculates the dot product of two vec4's\n *\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @returns {Number} dot product of a and b\n */\n\nexport function dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2] + a[3] * b[3];\n}\n/**\n * Returns the cross-product of three vectors in a 4-dimensional space\n *\n * @param {ReadonlyVec4} result the receiving vector\n * @param {ReadonlyVec4} U the first vector\n * @param {ReadonlyVec4} V the second vector\n * @param {ReadonlyVec4} W the third vector\n * @returns {vec4} result\n */\n\nexport function cross(out, u, v, w) {\n  var A = v[0] * w[1] - v[1] * w[0],\n      B = v[0] * w[2] - v[2] * w[0],\n      C = v[0] * w[3] - v[3] * w[0],\n      D = v[1] * w[2] - v[2] * w[1],\n      E = v[1] * w[3] - v[3] * w[1],\n      F = v[2] * w[3] - v[3] * w[2];\n  var G = u[0];\n  var H = u[1];\n  var I = u[2];\n  var J = u[3];\n  out[0] = H * F - I * E + J * D;\n  out[1] = -(G * F) + I * C - J * B;\n  out[2] = G * E - H * C + J * A;\n  out[3] = -(G * D) + H * B - I * A;\n  return out;\n}\n/**\n * Performs a linear interpolation between two vec4's\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the first operand\n * @param {ReadonlyVec4} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec4} out\n */\n\nexport function lerp(out, a, b, t) {\n  var ax = a[0];\n  var ay = a[1];\n  var az = a[2];\n  var aw = a[3];\n  out[0] = ax + t * (b[0] - ax);\n  out[1] = ay + t * (b[1] - ay);\n  out[2] = az + t * (b[2] - az);\n  out[3] = aw + t * (b[3] - aw);\n  return out;\n}\n/**\n * Generates a random vector with the given scale\n *\n * @param {vec4} out the receiving vector\n * @param {Number} [scale] Length of the resulting vector. If ommitted, a unit vector will be returned\n * @returns {vec4} out\n */\n\nexport function random(out, scale) {\n  scale = scale || 1.0; // Marsaglia, George. Choosing a Point from the Surface of a\n  // Sphere. Ann. Math. Statist. 43 (1972), no. 2, 645--646.\n  // http://projecteuclid.org/euclid.aoms/1177692644;\n\n  var v1, v2, v3, v4;\n  var s1, s2;\n\n  do {\n    v1 = glMatrix.RANDOM() * 2 - 1;\n    v2 = glMatrix.RANDOM() * 2 - 1;\n    s1 = v1 * v1 + v2 * v2;\n  } while (s1 >= 1);\n\n  do {\n    v3 = glMatrix.RANDOM() * 2 - 1;\n    v4 = glMatrix.RANDOM() * 2 - 1;\n    s2 = v3 * v3 + v4 * v4;\n  } while (s2 >= 1);\n\n  var d = Math.sqrt((1 - s1) / s2);\n  out[0] = scale * v1;\n  out[1] = scale * v2;\n  out[2] = scale * v3 * d;\n  out[3] = scale * v4 * d;\n  return out;\n}\n/**\n * Transforms the vec4 with a mat4.\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the vector to transform\n * @param {ReadonlyMat4} m matrix to transform with\n * @returns {vec4} out\n */\n\nexport function transformMat4(out, a, m) {\n  var x = a[0],\n      y = a[1],\n      z = a[2],\n      w = a[3];\n  out[0] = m[0] * x + m[4] * y + m[8] * z + m[12] * w;\n  out[1] = m[1] * x + m[5] * y + m[9] * z + m[13] * w;\n  out[2] = m[2] * x + m[6] * y + m[10] * z + m[14] * w;\n  out[3] = m[3] * x + m[7] * y + m[11] * z + m[15] * w;\n  return out;\n}\n/**\n * Transforms the vec4 with a quat\n *\n * @param {vec4} out the receiving vector\n * @param {ReadonlyVec4} a the vector to transform\n * @param {ReadonlyQuat} q quaternion to transform with\n * @returns {vec4} out\n */\n\nexport function transformQuat(out, a, q) {\n  var x = a[0],\n      y = a[1],\n      z = a[2];\n  var qx = q[0],\n      qy = q[1],\n      qz = q[2],\n      qw = q[3]; // calculate quat * vec\n\n  var ix = qw * x + qy * z - qz * y;\n  var iy = qw * y + qz * x - qx * z;\n  var iz = qw * z + qx * y - qy * x;\n  var iw = -qx * x - qy * y - qz * z; // calculate result * inverse quat\n\n  out[0] = ix * qw + iw * -qx + iy * -qz - iz * -qy;\n  out[1] = iy * qw + iw * -qy + iz * -qx - ix * -qz;\n  out[2] = iz * qw + iw * -qz + ix * -qy - iy * -qx;\n  out[3] = a[3];\n  return out;\n}\n/**\n * Set the components of a vec4 to zero\n *\n * @param {vec4} out the receiving vector\n * @returns {vec4} out\n */\n\nexport function zero(out) {\n  out[0] = 0.0;\n  out[1] = 0.0;\n  out[2] = 0.0;\n  out[3] = 0.0;\n  return out;\n}\n/**\n * Returns a string representation of a vector\n *\n * @param {ReadonlyVec4} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\n\nexport function str(a) {\n  return \"vec4(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \")\";\n}\n/**\n * Returns whether or not the vectors have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyVec4} a The first vector.\n * @param {ReadonlyVec4} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3];\n}\n/**\n * Returns whether or not the vectors have approximately the same elements in the same position.\n *\n * @param {ReadonlyVec4} a The first vector.\n * @param {ReadonlyVec4} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3));\n}\n/**\n * Alias for {@link vec4.subtract}\n * @function\n */\n\nexport var sub = subtract;\n/**\n * Alias for {@link vec4.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link vec4.divide}\n * @function\n */\n\nexport var div = divide;\n/**\n * Alias for {@link vec4.distance}\n * @function\n */\n\nexport var dist = distance;\n/**\n * Alias for {@link vec4.squaredDistance}\n * @function\n */\n\nexport var sqrDist = squaredDistance;\n/**\n * Alias for {@link vec4.length}\n * @function\n */\n\nexport var len = length;\n/**\n * Alias for {@link vec4.squaredLength}\n * @function\n */\n\nexport var sqrLen = squaredLength;\n/**\n * Perform some operation over an array of vec4s.\n *\n * @param {Array} a the array of vectors to iterate over\n * @param {Number} stride Number of elements between the start of each vec4. If 0 assumes tightly packed\n * @param {Number} offset Number of elements to skip at the beginning of the array\n * @param {Number} count Number of vec4s to iterate over. If 0 iterates over entire array\n * @param {Function} fn Function to call for each vector in the array\n * @param {Object} [arg] additional argument to pass to fn\n * @returns {Array} a\n * @function\n */\n\nexport var forEach = function () {\n  var vec = create();\n  return function (a, stride, offset, count, fn, arg) {\n    var i, l;\n\n    if (!stride) {\n      stride = 4;\n    }\n\n    if (!offset) {\n      offset = 0;\n    }\n\n    if (count) {\n      l = Math.min(count * stride + offset, a.length);\n    } else {\n      l = a.length;\n    }\n\n    for (i = offset; i < l; i += stride) {\n      vec[0] = a[i];\n      vec[1] = a[i + 1];\n      vec[2] = a[i + 2];\n      vec[3] = a[i + 3];\n      fn(vec, vec, arg);\n      a[i] = vec[0];\n      a[i + 1] = vec[1];\n      a[i + 2] = vec[2];\n      a[i + 3] = vec[3];\n    }\n\n    return a;\n  };\n}();", "import * as glMatrix from \"./common.js\";\nimport * as quat from \"./quat.js\";\nimport * as mat4 from \"./mat4.js\";\n/**\n * Dual Quaternion<br>\n * Format: [real, dual]<br>\n * Quaternion format: XYZW<br>\n * Make sure to have normalized dual quaternions, otherwise the functions may not work as intended.<br>\n * @module quat2\n */\n\n/**\n * Creates a new identity dual quat\n *\n * @returns {quat2} a new dual quaternion [real -> rotation, dual -> translation]\n */\n\nexport function create() {\n  var dq = new glMatrix.ARRAY_TYPE(8);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    dq[0] = 0;\n    dq[1] = 0;\n    dq[2] = 0;\n    dq[4] = 0;\n    dq[5] = 0;\n    dq[6] = 0;\n    dq[7] = 0;\n  }\n\n  dq[3] = 1;\n  return dq;\n}\n/**\n * Creates a new quat initialized with values from an existing quaternion\n *\n * @param {ReadonlyQuat2} a dual quaternion to clone\n * @returns {quat2} new dual quaternion\n * @function\n */\n\nexport function clone(a) {\n  var dq = new glMatrix.ARRAY_TYPE(8);\n  dq[0] = a[0];\n  dq[1] = a[1];\n  dq[2] = a[2];\n  dq[3] = a[3];\n  dq[4] = a[4];\n  dq[5] = a[5];\n  dq[6] = a[6];\n  dq[7] = a[7];\n  return dq;\n}\n/**\n * Creates a new dual quat initialized with the given values\n *\n * @param {Number} x1 X component\n * @param {Number} y1 Y component\n * @param {Number} z1 Z component\n * @param {Number} w1 W component\n * @param {Number} x2 X component\n * @param {Number} y2 Y component\n * @param {Number} z2 Z component\n * @param {Number} w2 W component\n * @returns {quat2} new dual quaternion\n * @function\n */\n\nexport function fromValues(x1, y1, z1, w1, x2, y2, z2, w2) {\n  var dq = new glMatrix.ARRAY_TYPE(8);\n  dq[0] = x1;\n  dq[1] = y1;\n  dq[2] = z1;\n  dq[3] = w1;\n  dq[4] = x2;\n  dq[5] = y2;\n  dq[6] = z2;\n  dq[7] = w2;\n  return dq;\n}\n/**\n * Creates a new dual quat from the given values (quat and translation)\n *\n * @param {Number} x1 X component\n * @param {Number} y1 Y component\n * @param {Number} z1 Z component\n * @param {Number} w1 W component\n * @param {Number} x2 X component (translation)\n * @param {Number} y2 Y component (translation)\n * @param {Number} z2 Z component (translation)\n * @returns {quat2} new dual quaternion\n * @function\n */\n\nexport function fromRotationTranslationValues(x1, y1, z1, w1, x2, y2, z2) {\n  var dq = new glMatrix.ARRAY_TYPE(8);\n  dq[0] = x1;\n  dq[1] = y1;\n  dq[2] = z1;\n  dq[3] = w1;\n  var ax = x2 * 0.5,\n      ay = y2 * 0.5,\n      az = z2 * 0.5;\n  dq[4] = ax * w1 + ay * z1 - az * y1;\n  dq[5] = ay * w1 + az * x1 - ax * z1;\n  dq[6] = az * w1 + ax * y1 - ay * x1;\n  dq[7] = -ax * x1 - ay * y1 - az * z1;\n  return dq;\n}\n/**\n * Creates a dual quat from a quaternion and a translation\n *\n * @param {ReadonlyQuat2} dual quaternion receiving operation result\n * @param {ReadonlyQuat} q a normalized quaternion\n * @param {ReadonlyVec3} t tranlation vector\n * @returns {quat2} dual quaternion receiving operation result\n * @function\n */\n\nexport function fromRotationTranslation(out, q, t) {\n  var ax = t[0] * 0.5,\n      ay = t[1] * 0.5,\n      az = t[2] * 0.5,\n      bx = q[0],\n      by = q[1],\n      bz = q[2],\n      bw = q[3];\n  out[0] = bx;\n  out[1] = by;\n  out[2] = bz;\n  out[3] = bw;\n  out[4] = ax * bw + ay * bz - az * by;\n  out[5] = ay * bw + az * bx - ax * bz;\n  out[6] = az * bw + ax * by - ay * bx;\n  out[7] = -ax * bx - ay * by - az * bz;\n  return out;\n}\n/**\n * Creates a dual quat from a translation\n *\n * @param {ReadonlyQuat2} dual quaternion receiving operation result\n * @param {ReadonlyVec3} t translation vector\n * @returns {quat2} dual quaternion receiving operation result\n * @function\n */\n\nexport function fromTranslation(out, t) {\n  out[0] = 0;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = t[0] * 0.5;\n  out[5] = t[1] * 0.5;\n  out[6] = t[2] * 0.5;\n  out[7] = 0;\n  return out;\n}\n/**\n * Creates a dual quat from a quaternion\n *\n * @param {ReadonlyQuat2} dual quaternion receiving operation result\n * @param {ReadonlyQuat} q the quaternion\n * @returns {quat2} dual quaternion receiving operation result\n * @function\n */\n\nexport function fromRotation(out, q) {\n  out[0] = q[0];\n  out[1] = q[1];\n  out[2] = q[2];\n  out[3] = q[3];\n  out[4] = 0;\n  out[5] = 0;\n  out[6] = 0;\n  out[7] = 0;\n  return out;\n}\n/**\n * Creates a new dual quat from a matrix (4x4)\n *\n * @param {quat2} out the dual quaternion\n * @param {ReadonlyMat4} a the matrix\n * @returns {quat2} dual quat receiving operation result\n * @function\n */\n\nexport function fromMat4(out, a) {\n  //TODO Optimize this\n  var outer = quat.create();\n  mat4.getRotation(outer, a);\n  var t = new glMatrix.ARRAY_TYPE(3);\n  mat4.getTranslation(t, a);\n  fromRotationTranslation(out, outer, t);\n  return out;\n}\n/**\n * Copy the values from one dual quat to another\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the source dual quaternion\n * @returns {quat2} out\n * @function\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  out[2] = a[2];\n  out[3] = a[3];\n  out[4] = a[4];\n  out[5] = a[5];\n  out[6] = a[6];\n  out[7] = a[7];\n  return out;\n}\n/**\n * Set a dual quat to the identity dual quaternion\n *\n * @param {quat2} out the receiving quaternion\n * @returns {quat2} out\n */\n\nexport function identity(out) {\n  out[0] = 0;\n  out[1] = 0;\n  out[2] = 0;\n  out[3] = 1;\n  out[4] = 0;\n  out[5] = 0;\n  out[6] = 0;\n  out[7] = 0;\n  return out;\n}\n/**\n * Set the components of a dual quat to the given values\n *\n * @param {quat2} out the receiving quaternion\n * @param {Number} x1 X component\n * @param {Number} y1 Y component\n * @param {Number} z1 Z component\n * @param {Number} w1 W component\n * @param {Number} x2 X component\n * @param {Number} y2 Y component\n * @param {Number} z2 Z component\n * @param {Number} w2 W component\n * @returns {quat2} out\n * @function\n */\n\nexport function set(out, x1, y1, z1, w1, x2, y2, z2, w2) {\n  out[0] = x1;\n  out[1] = y1;\n  out[2] = z1;\n  out[3] = w1;\n  out[4] = x2;\n  out[5] = y2;\n  out[6] = z2;\n  out[7] = w2;\n  return out;\n}\n/**\n * Gets the real part of a dual quat\n * @param  {quat} out real part\n * @param  {ReadonlyQuat2} a Dual Quaternion\n * @return {quat} real part\n */\n\nexport var getReal = quat.copy;\n/**\n * Gets the dual part of a dual quat\n * @param  {quat} out dual part\n * @param  {ReadonlyQuat2} a Dual Quaternion\n * @return {quat} dual part\n */\n\nexport function getDual(out, a) {\n  out[0] = a[4];\n  out[1] = a[5];\n  out[2] = a[6];\n  out[3] = a[7];\n  return out;\n}\n/**\n * Set the real component of a dual quat to the given quaternion\n *\n * @param {quat2} out the receiving quaternion\n * @param {ReadonlyQuat} q a quaternion representing the real part\n * @returns {quat2} out\n * @function\n */\n\nexport var setReal = quat.copy;\n/**\n * Set the dual component of a dual quat to the given quaternion\n *\n * @param {quat2} out the receiving quaternion\n * @param {ReadonlyQuat} q a quaternion representing the dual part\n * @returns {quat2} out\n * @function\n */\n\nexport function setDual(out, q) {\n  out[4] = q[0];\n  out[5] = q[1];\n  out[6] = q[2];\n  out[7] = q[3];\n  return out;\n}\n/**\n * Gets the translation of a normalized dual quat\n * @param  {vec3} out translation\n * @param  {ReadonlyQuat2} a Dual Quaternion to be decomposed\n * @return {vec3} translation\n */\n\nexport function getTranslation(out, a) {\n  var ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7],\n      bx = -a[0],\n      by = -a[1],\n      bz = -a[2],\n      bw = a[3];\n  out[0] = (ax * bw + aw * bx + ay * bz - az * by) * 2;\n  out[1] = (ay * bw + aw * by + az * bx - ax * bz) * 2;\n  out[2] = (az * bw + aw * bz + ax * by - ay * bx) * 2;\n  return out;\n}\n/**\n * Translates a dual quat by the given vector\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to translate\n * @param {ReadonlyVec3} v vector to translate by\n * @returns {quat2} out\n */\n\nexport function translate(out, a, v) {\n  var ax1 = a[0],\n      ay1 = a[1],\n      az1 = a[2],\n      aw1 = a[3],\n      bx1 = v[0] * 0.5,\n      by1 = v[1] * 0.5,\n      bz1 = v[2] * 0.5,\n      ax2 = a[4],\n      ay2 = a[5],\n      az2 = a[6],\n      aw2 = a[7];\n  out[0] = ax1;\n  out[1] = ay1;\n  out[2] = az1;\n  out[3] = aw1;\n  out[4] = aw1 * bx1 + ay1 * bz1 - az1 * by1 + ax2;\n  out[5] = aw1 * by1 + az1 * bx1 - ax1 * bz1 + ay2;\n  out[6] = aw1 * bz1 + ax1 * by1 - ay1 * bx1 + az2;\n  out[7] = -ax1 * bx1 - ay1 * by1 - az1 * bz1 + aw2;\n  return out;\n}\n/**\n * Rotates a dual quat around the X axis\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @param {number} rad how far should the rotation be\n * @returns {quat2} out\n */\n\nexport function rotateX(out, a, rad) {\n  var bx = -a[0],\n      by = -a[1],\n      bz = -a[2],\n      bw = a[3],\n      ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7],\n      ax1 = ax * bw + aw * bx + ay * bz - az * by,\n      ay1 = ay * bw + aw * by + az * bx - ax * bz,\n      az1 = az * bw + aw * bz + ax * by - ay * bx,\n      aw1 = aw * bw - ax * bx - ay * by - az * bz;\n  quat.rotateX(out, a, rad);\n  bx = out[0];\n  by = out[1];\n  bz = out[2];\n  bw = out[3];\n  out[4] = ax1 * bw + aw1 * bx + ay1 * bz - az1 * by;\n  out[5] = ay1 * bw + aw1 * by + az1 * bx - ax1 * bz;\n  out[6] = az1 * bw + aw1 * bz + ax1 * by - ay1 * bx;\n  out[7] = aw1 * bw - ax1 * bx - ay1 * by - az1 * bz;\n  return out;\n}\n/**\n * Rotates a dual quat around the Y axis\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @param {number} rad how far should the rotation be\n * @returns {quat2} out\n */\n\nexport function rotateY(out, a, rad) {\n  var bx = -a[0],\n      by = -a[1],\n      bz = -a[2],\n      bw = a[3],\n      ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7],\n      ax1 = ax * bw + aw * bx + ay * bz - az * by,\n      ay1 = ay * bw + aw * by + az * bx - ax * bz,\n      az1 = az * bw + aw * bz + ax * by - ay * bx,\n      aw1 = aw * bw - ax * bx - ay * by - az * bz;\n  quat.rotateY(out, a, rad);\n  bx = out[0];\n  by = out[1];\n  bz = out[2];\n  bw = out[3];\n  out[4] = ax1 * bw + aw1 * bx + ay1 * bz - az1 * by;\n  out[5] = ay1 * bw + aw1 * by + az1 * bx - ax1 * bz;\n  out[6] = az1 * bw + aw1 * bz + ax1 * by - ay1 * bx;\n  out[7] = aw1 * bw - ax1 * bx - ay1 * by - az1 * bz;\n  return out;\n}\n/**\n * Rotates a dual quat around the Z axis\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @param {number} rad how far should the rotation be\n * @returns {quat2} out\n */\n\nexport function rotateZ(out, a, rad) {\n  var bx = -a[0],\n      by = -a[1],\n      bz = -a[2],\n      bw = a[3],\n      ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7],\n      ax1 = ax * bw + aw * bx + ay * bz - az * by,\n      ay1 = ay * bw + aw * by + az * bx - ax * bz,\n      az1 = az * bw + aw * bz + ax * by - ay * bx,\n      aw1 = aw * bw - ax * bx - ay * by - az * bz;\n  quat.rotateZ(out, a, rad);\n  bx = out[0];\n  by = out[1];\n  bz = out[2];\n  bw = out[3];\n  out[4] = ax1 * bw + aw1 * bx + ay1 * bz - az1 * by;\n  out[5] = ay1 * bw + aw1 * by + az1 * bx - ax1 * bz;\n  out[6] = az1 * bw + aw1 * bz + ax1 * by - ay1 * bx;\n  out[7] = aw1 * bw - ax1 * bx - ay1 * by - az1 * bz;\n  return out;\n}\n/**\n * Rotates a dual quat by a given quaternion (a * q)\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @param {ReadonlyQuat} q quaternion to rotate by\n * @returns {quat2} out\n */\n\nexport function rotateByQuatAppend(out, a, q) {\n  var qx = q[0],\n      qy = q[1],\n      qz = q[2],\n      qw = q[3],\n      ax = a[0],\n      ay = a[1],\n      az = a[2],\n      aw = a[3];\n  out[0] = ax * qw + aw * qx + ay * qz - az * qy;\n  out[1] = ay * qw + aw * qy + az * qx - ax * qz;\n  out[2] = az * qw + aw * qz + ax * qy - ay * qx;\n  out[3] = aw * qw - ax * qx - ay * qy - az * qz;\n  ax = a[4];\n  ay = a[5];\n  az = a[6];\n  aw = a[7];\n  out[4] = ax * qw + aw * qx + ay * qz - az * qy;\n  out[5] = ay * qw + aw * qy + az * qx - ax * qz;\n  out[6] = az * qw + aw * qz + ax * qy - ay * qx;\n  out[7] = aw * qw - ax * qx - ay * qy - az * qz;\n  return out;\n}\n/**\n * Rotates a dual quat by a given quaternion (q * a)\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat} q quaternion to rotate by\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @returns {quat2} out\n */\n\nexport function rotateByQuatPrepend(out, q, a) {\n  var qx = q[0],\n      qy = q[1],\n      qz = q[2],\n      qw = q[3],\n      bx = a[0],\n      by = a[1],\n      bz = a[2],\n      bw = a[3];\n  out[0] = qx * bw + qw * bx + qy * bz - qz * by;\n  out[1] = qy * bw + qw * by + qz * bx - qx * bz;\n  out[2] = qz * bw + qw * bz + qx * by - qy * bx;\n  out[3] = qw * bw - qx * bx - qy * by - qz * bz;\n  bx = a[4];\n  by = a[5];\n  bz = a[6];\n  bw = a[7];\n  out[4] = qx * bw + qw * bx + qy * bz - qz * by;\n  out[5] = qy * bw + qw * by + qz * bx - qx * bz;\n  out[6] = qz * bw + qw * bz + qx * by - qy * bx;\n  out[7] = qw * bw - qx * bx - qy * by - qz * bz;\n  return out;\n}\n/**\n * Rotates a dual quat around a given axis. Does the normalisation automatically\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the dual quaternion to rotate\n * @param {ReadonlyVec3} axis the axis to rotate around\n * @param {Number} rad how far the rotation should be\n * @returns {quat2} out\n */\n\nexport function rotateAroundAxis(out, a, axis, rad) {\n  //Special case for rad = 0\n  if (Math.abs(rad) < glMatrix.EPSILON) {\n    return copy(out, a);\n  }\n\n  var axisLength = Math.hypot(axis[0], axis[1], axis[2]);\n  rad = rad * 0.5;\n  var s = Math.sin(rad);\n  var bx = s * axis[0] / axisLength;\n  var by = s * axis[1] / axisLength;\n  var bz = s * axis[2] / axisLength;\n  var bw = Math.cos(rad);\n  var ax1 = a[0],\n      ay1 = a[1],\n      az1 = a[2],\n      aw1 = a[3];\n  out[0] = ax1 * bw + aw1 * bx + ay1 * bz - az1 * by;\n  out[1] = ay1 * bw + aw1 * by + az1 * bx - ax1 * bz;\n  out[2] = az1 * bw + aw1 * bz + ax1 * by - ay1 * bx;\n  out[3] = aw1 * bw - ax1 * bx - ay1 * by - az1 * bz;\n  var ax = a[4],\n      ay = a[5],\n      az = a[6],\n      aw = a[7];\n  out[4] = ax * bw + aw * bx + ay * bz - az * by;\n  out[5] = ay * bw + aw * by + az * bx - ax * bz;\n  out[6] = az * bw + aw * bz + ax * by - ay * bx;\n  out[7] = aw * bw - ax * bx - ay * by - az * bz;\n  return out;\n}\n/**\n * Adds two dual quat's\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the first operand\n * @param {ReadonlyQuat2} b the second operand\n * @returns {quat2} out\n * @function\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  out[2] = a[2] + b[2];\n  out[3] = a[3] + b[3];\n  out[4] = a[4] + b[4];\n  out[5] = a[5] + b[5];\n  out[6] = a[6] + b[6];\n  out[7] = a[7] + b[7];\n  return out;\n}\n/**\n * Multiplies two dual quat's\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a the first operand\n * @param {ReadonlyQuat2} b the second operand\n * @returns {quat2} out\n */\n\nexport function multiply(out, a, b) {\n  var ax0 = a[0],\n      ay0 = a[1],\n      az0 = a[2],\n      aw0 = a[3],\n      bx1 = b[4],\n      by1 = b[5],\n      bz1 = b[6],\n      bw1 = b[7],\n      ax1 = a[4],\n      ay1 = a[5],\n      az1 = a[6],\n      aw1 = a[7],\n      bx0 = b[0],\n      by0 = b[1],\n      bz0 = b[2],\n      bw0 = b[3];\n  out[0] = ax0 * bw0 + aw0 * bx0 + ay0 * bz0 - az0 * by0;\n  out[1] = ay0 * bw0 + aw0 * by0 + az0 * bx0 - ax0 * bz0;\n  out[2] = az0 * bw0 + aw0 * bz0 + ax0 * by0 - ay0 * bx0;\n  out[3] = aw0 * bw0 - ax0 * bx0 - ay0 * by0 - az0 * bz0;\n  out[4] = ax0 * bw1 + aw0 * bx1 + ay0 * bz1 - az0 * by1 + ax1 * bw0 + aw1 * bx0 + ay1 * bz0 - az1 * by0;\n  out[5] = ay0 * bw1 + aw0 * by1 + az0 * bx1 - ax0 * bz1 + ay1 * bw0 + aw1 * by0 + az1 * bx0 - ax1 * bz0;\n  out[6] = az0 * bw1 + aw0 * bz1 + ax0 * by1 - ay0 * bx1 + az1 * bw0 + aw1 * bz0 + ax1 * by0 - ay1 * bx0;\n  out[7] = aw0 * bw1 - ax0 * bx1 - ay0 * by1 - az0 * bz1 + aw1 * bw0 - ax1 * bx0 - ay1 * by0 - az1 * bz0;\n  return out;\n}\n/**\n * Alias for {@link quat2.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Scales a dual quat by a scalar number\n *\n * @param {quat2} out the receiving dual quat\n * @param {ReadonlyQuat2} a the dual quat to scale\n * @param {Number} b amount to scale the dual quat by\n * @returns {quat2} out\n * @function\n */\n\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  out[2] = a[2] * b;\n  out[3] = a[3] * b;\n  out[4] = a[4] * b;\n  out[5] = a[5] * b;\n  out[6] = a[6] * b;\n  out[7] = a[7] * b;\n  return out;\n}\n/**\n * Calculates the dot product of two dual quat's (The dot product of the real parts)\n *\n * @param {ReadonlyQuat2} a the first operand\n * @param {ReadonlyQuat2} b the second operand\n * @returns {Number} dot product of a and b\n * @function\n */\n\nexport var dot = quat.dot;\n/**\n * Performs a linear interpolation between two dual quats's\n * NOTE: The resulting dual quaternions won't always be normalized (The error is most noticeable when t = 0.5)\n *\n * @param {quat2} out the receiving dual quat\n * @param {ReadonlyQuat2} a the first operand\n * @param {ReadonlyQuat2} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {quat2} out\n */\n\nexport function lerp(out, a, b, t) {\n  var mt = 1 - t;\n  if (dot(a, b) < 0) t = -t;\n  out[0] = a[0] * mt + b[0] * t;\n  out[1] = a[1] * mt + b[1] * t;\n  out[2] = a[2] * mt + b[2] * t;\n  out[3] = a[3] * mt + b[3] * t;\n  out[4] = a[4] * mt + b[4] * t;\n  out[5] = a[5] * mt + b[5] * t;\n  out[6] = a[6] * mt + b[6] * t;\n  out[7] = a[7] * mt + b[7] * t;\n  return out;\n}\n/**\n * Calculates the inverse of a dual quat. If they are normalized, conjugate is cheaper\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a dual quat to calculate inverse of\n * @returns {quat2} out\n */\n\nexport function invert(out, a) {\n  var sqlen = squaredLength(a);\n  out[0] = -a[0] / sqlen;\n  out[1] = -a[1] / sqlen;\n  out[2] = -a[2] / sqlen;\n  out[3] = a[3] / sqlen;\n  out[4] = -a[4] / sqlen;\n  out[5] = -a[5] / sqlen;\n  out[6] = -a[6] / sqlen;\n  out[7] = a[7] / sqlen;\n  return out;\n}\n/**\n * Calculates the conjugate of a dual quat\n * If the dual quaternion is normalized, this function is faster than quat2.inverse and produces the same result.\n *\n * @param {quat2} out the receiving quaternion\n * @param {ReadonlyQuat2} a quat to calculate conjugate of\n * @returns {quat2} out\n */\n\nexport function conjugate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  out[2] = -a[2];\n  out[3] = a[3];\n  out[4] = -a[4];\n  out[5] = -a[5];\n  out[6] = -a[6];\n  out[7] = a[7];\n  return out;\n}\n/**\n * Calculates the length of a dual quat\n *\n * @param {ReadonlyQuat2} a dual quat to calculate length of\n * @returns {Number} length of a\n * @function\n */\n\nexport var length = quat.length;\n/**\n * Alias for {@link quat2.length}\n * @function\n */\n\nexport var len = length;\n/**\n * Calculates the squared length of a dual quat\n *\n * @param {ReadonlyQuat2} a dual quat to calculate squared length of\n * @returns {Number} squared length of a\n * @function\n */\n\nexport var squaredLength = quat.squaredLength;\n/**\n * Alias for {@link quat2.squaredLength}\n * @function\n */\n\nexport var sqrLen = squaredLength;\n/**\n * Normalize a dual quat\n *\n * @param {quat2} out the receiving dual quaternion\n * @param {ReadonlyQuat2} a dual quaternion to normalize\n * @returns {quat2} out\n * @function\n */\n\nexport function normalize(out, a) {\n  var magnitude = squaredLength(a);\n\n  if (magnitude > 0) {\n    magnitude = Math.sqrt(magnitude);\n    var a0 = a[0] / magnitude;\n    var a1 = a[1] / magnitude;\n    var a2 = a[2] / magnitude;\n    var a3 = a[3] / magnitude;\n    var b0 = a[4];\n    var b1 = a[5];\n    var b2 = a[6];\n    var b3 = a[7];\n    var a_dot_b = a0 * b0 + a1 * b1 + a2 * b2 + a3 * b3;\n    out[0] = a0;\n    out[1] = a1;\n    out[2] = a2;\n    out[3] = a3;\n    out[4] = (b0 - a0 * a_dot_b) / magnitude;\n    out[5] = (b1 - a1 * a_dot_b) / magnitude;\n    out[6] = (b2 - a2 * a_dot_b) / magnitude;\n    out[7] = (b3 - a3 * a_dot_b) / magnitude;\n  }\n\n  return out;\n}\n/**\n * Returns a string representation of a dual quatenion\n *\n * @param {ReadonlyQuat2} a dual quaternion to represent as a string\n * @returns {String} string representation of the dual quat\n */\n\nexport function str(a) {\n  return \"quat2(\" + a[0] + \", \" + a[1] + \", \" + a[2] + \", \" + a[3] + \", \" + a[4] + \", \" + a[5] + \", \" + a[6] + \", \" + a[7] + \")\";\n}\n/**\n * Returns whether or not the dual quaternions have exactly the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyQuat2} a the first dual quaternion.\n * @param {ReadonlyQuat2} b the second dual quaternion.\n * @returns {Boolean} true if the dual quaternions are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1] && a[2] === b[2] && a[3] === b[3] && a[4] === b[4] && a[5] === b[5] && a[6] === b[6] && a[7] === b[7];\n}\n/**\n * Returns whether or not the dual quaternions have approximately the same elements in the same position.\n *\n * @param {ReadonlyQuat2} a the first dual quat.\n * @param {ReadonlyQuat2} b the second dual quat.\n * @returns {Boolean} true if the dual quats are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1],\n      a2 = a[2],\n      a3 = a[3],\n      a4 = a[4],\n      a5 = a[5],\n      a6 = a[6],\n      a7 = a[7];\n  var b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3],\n      b4 = b[4],\n      b5 = b[5],\n      b6 = b[6],\n      b7 = b[7];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1)) && Math.abs(a2 - b2) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a2), Math.abs(b2)) && Math.abs(a3 - b3) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a3), Math.abs(b3)) && Math.abs(a4 - b4) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a4), Math.abs(b4)) && Math.abs(a5 - b5) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a5), Math.abs(b5)) && Math.abs(a6 - b6) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a6), Math.abs(b6)) && Math.abs(a7 - b7) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a7), Math.abs(b7));\n}", "import * as glMatrix from \"./common.js\";\n/**\n * 2 Dimensional Vector\n * @module vec2\n */\n\n/**\n * Creates a new, empty vec2\n *\n * @returns {vec2} a new 2D vector\n */\n\nexport function create() {\n  var out = new glMatrix.ARRAY_TYPE(2);\n\n  if (glMatrix.ARRAY_TYPE != Float32Array) {\n    out[0] = 0;\n    out[1] = 0;\n  }\n\n  return out;\n}\n/**\n * Creates a new vec2 initialized with values from an existing vector\n *\n * @param {ReadonlyVec2} a vector to clone\n * @returns {vec2} a new 2D vector\n */\n\nexport function clone(a) {\n  var out = new glMatrix.ARRAY_TYPE(2);\n  out[0] = a[0];\n  out[1] = a[1];\n  return out;\n}\n/**\n * Creates a new vec2 initialized with the given values\n *\n * @param {Number} x X component\n * @param {Number} y Y component\n * @returns {vec2} a new 2D vector\n */\n\nexport function fromValues(x, y) {\n  var out = new glMatrix.ARRAY_TYPE(2);\n  out[0] = x;\n  out[1] = y;\n  return out;\n}\n/**\n * Copy the values from one vec2 to another\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the source vector\n * @returns {vec2} out\n */\n\nexport function copy(out, a) {\n  out[0] = a[0];\n  out[1] = a[1];\n  return out;\n}\n/**\n * Set the components of a vec2 to the given values\n *\n * @param {vec2} out the receiving vector\n * @param {Number} x X component\n * @param {Number} y Y component\n * @returns {vec2} out\n */\n\nexport function set(out, x, y) {\n  out[0] = x;\n  out[1] = y;\n  return out;\n}\n/**\n * Adds two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function add(out, a, b) {\n  out[0] = a[0] + b[0];\n  out[1] = a[1] + b[1];\n  return out;\n}\n/**\n * Subtracts vector b from vector a\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function subtract(out, a, b) {\n  out[0] = a[0] - b[0];\n  out[1] = a[1] - b[1];\n  return out;\n}\n/**\n * Multiplies two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function multiply(out, a, b) {\n  out[0] = a[0] * b[0];\n  out[1] = a[1] * b[1];\n  return out;\n}\n/**\n * Divides two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function divide(out, a, b) {\n  out[0] = a[0] / b[0];\n  out[1] = a[1] / b[1];\n  return out;\n}\n/**\n * Math.ceil the components of a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to ceil\n * @returns {vec2} out\n */\n\nexport function ceil(out, a) {\n  out[0] = Math.ceil(a[0]);\n  out[1] = Math.ceil(a[1]);\n  return out;\n}\n/**\n * Math.floor the components of a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to floor\n * @returns {vec2} out\n */\n\nexport function floor(out, a) {\n  out[0] = Math.floor(a[0]);\n  out[1] = Math.floor(a[1]);\n  return out;\n}\n/**\n * Returns the minimum of two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function min(out, a, b) {\n  out[0] = Math.min(a[0], b[0]);\n  out[1] = Math.min(a[1], b[1]);\n  return out;\n}\n/**\n * Returns the maximum of two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec2} out\n */\n\nexport function max(out, a, b) {\n  out[0] = Math.max(a[0], b[0]);\n  out[1] = Math.max(a[1], b[1]);\n  return out;\n}\n/**\n * Math.round the components of a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to round\n * @returns {vec2} out\n */\n\nexport function round(out, a) {\n  out[0] = Math.round(a[0]);\n  out[1] = Math.round(a[1]);\n  return out;\n}\n/**\n * Scales a vec2 by a scalar number\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the vector to scale\n * @param {Number} b amount to scale the vector by\n * @returns {vec2} out\n */\n\nexport function scale(out, a, b) {\n  out[0] = a[0] * b;\n  out[1] = a[1] * b;\n  return out;\n}\n/**\n * Adds two vec2's after scaling the second operand by a scalar value\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @param {Number} scale the amount to scale b by before adding\n * @returns {vec2} out\n */\n\nexport function scaleAndAdd(out, a, b, scale) {\n  out[0] = a[0] + b[0] * scale;\n  out[1] = a[1] + b[1] * scale;\n  return out;\n}\n/**\n * Calculates the euclidian distance between two vec2's\n *\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {Number} distance between a and b\n */\n\nexport function distance(a, b) {\n  var x = b[0] - a[0],\n      y = b[1] - a[1];\n  return Math.hypot(x, y);\n}\n/**\n * Calculates the squared euclidian distance between two vec2's\n *\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {Number} squared distance between a and b\n */\n\nexport function squaredDistance(a, b) {\n  var x = b[0] - a[0],\n      y = b[1] - a[1];\n  return x * x + y * y;\n}\n/**\n * Calculates the length of a vec2\n *\n * @param {ReadonlyVec2} a vector to calculate length of\n * @returns {Number} length of a\n */\n\nexport function length(a) {\n  var x = a[0],\n      y = a[1];\n  return Math.hypot(x, y);\n}\n/**\n * Calculates the squared length of a vec2\n *\n * @param {ReadonlyVec2} a vector to calculate squared length of\n * @returns {Number} squared length of a\n */\n\nexport function squaredLength(a) {\n  var x = a[0],\n      y = a[1];\n  return x * x + y * y;\n}\n/**\n * Negates the components of a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to negate\n * @returns {vec2} out\n */\n\nexport function negate(out, a) {\n  out[0] = -a[0];\n  out[1] = -a[1];\n  return out;\n}\n/**\n * Returns the inverse of the components of a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to invert\n * @returns {vec2} out\n */\n\nexport function inverse(out, a) {\n  out[0] = 1.0 / a[0];\n  out[1] = 1.0 / a[1];\n  return out;\n}\n/**\n * Normalize a vec2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a vector to normalize\n * @returns {vec2} out\n */\n\nexport function normalize(out, a) {\n  var x = a[0],\n      y = a[1];\n  var len = x * x + y * y;\n\n  if (len > 0) {\n    //TODO: evaluate use of glm_invsqrt here?\n    len = 1 / Math.sqrt(len);\n  }\n\n  out[0] = a[0] * len;\n  out[1] = a[1] * len;\n  return out;\n}\n/**\n * Calculates the dot product of two vec2's\n *\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {Number} dot product of a and b\n */\n\nexport function dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Computes the cross product of two vec2's\n * Note that the cross product must by definition produce a 3D vector\n *\n * @param {vec3} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @returns {vec3} out\n */\n\nexport function cross(out, a, b) {\n  var z = a[0] * b[1] - a[1] * b[0];\n  out[0] = out[1] = 0;\n  out[2] = z;\n  return out;\n}\n/**\n * Performs a linear interpolation between two vec2's\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the first operand\n * @param {ReadonlyVec2} b the second operand\n * @param {Number} t interpolation amount, in the range [0-1], between the two inputs\n * @returns {vec2} out\n */\n\nexport function lerp(out, a, b, t) {\n  var ax = a[0],\n      ay = a[1];\n  out[0] = ax + t * (b[0] - ax);\n  out[1] = ay + t * (b[1] - ay);\n  return out;\n}\n/**\n * Generates a random vector with the given scale\n *\n * @param {vec2} out the receiving vector\n * @param {Number} [scale] Length of the resulting vector. If ommitted, a unit vector will be returned\n * @returns {vec2} out\n */\n\nexport function random(out, scale) {\n  scale = scale || 1.0;\n  var r = glMatrix.RANDOM() * 2.0 * Math.PI;\n  out[0] = Math.cos(r) * scale;\n  out[1] = Math.sin(r) * scale;\n  return out;\n}\n/**\n * Transforms the vec2 with a mat2\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the vector to transform\n * @param {ReadonlyMat2} m matrix to transform with\n * @returns {vec2} out\n */\n\nexport function transformMat2(out, a, m) {\n  var x = a[0],\n      y = a[1];\n  out[0] = m[0] * x + m[2] * y;\n  out[1] = m[1] * x + m[3] * y;\n  return out;\n}\n/**\n * Transforms the vec2 with a mat2d\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the vector to transform\n * @param {ReadonlyMat2d} m matrix to transform with\n * @returns {vec2} out\n */\n\nexport function transformMat2d(out, a, m) {\n  var x = a[0],\n      y = a[1];\n  out[0] = m[0] * x + m[2] * y + m[4];\n  out[1] = m[1] * x + m[3] * y + m[5];\n  return out;\n}\n/**\n * Transforms the vec2 with a mat3\n * 3rd vector component is implicitly '1'\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the vector to transform\n * @param {ReadonlyMat3} m matrix to transform with\n * @returns {vec2} out\n */\n\nexport function transformMat3(out, a, m) {\n  var x = a[0],\n      y = a[1];\n  out[0] = m[0] * x + m[3] * y + m[6];\n  out[1] = m[1] * x + m[4] * y + m[7];\n  return out;\n}\n/**\n * Transforms the vec2 with a mat4\n * 3rd vector component is implicitly '0'\n * 4th vector component is implicitly '1'\n *\n * @param {vec2} out the receiving vector\n * @param {ReadonlyVec2} a the vector to transform\n * @param {ReadonlyMat4} m matrix to transform with\n * @returns {vec2} out\n */\n\nexport function transformMat4(out, a, m) {\n  var x = a[0];\n  var y = a[1];\n  out[0] = m[0] * x + m[4] * y + m[12];\n  out[1] = m[1] * x + m[5] * y + m[13];\n  return out;\n}\n/**\n * Rotate a 2D vector\n * @param {vec2} out The receiving vec2\n * @param {ReadonlyVec2} a The vec2 point to rotate\n * @param {ReadonlyVec2} b The origin of the rotation\n * @param {Number} rad The angle of rotation in radians\n * @returns {vec2} out\n */\n\nexport function rotate(out, a, b, rad) {\n  //Translate point to the origin\n  var p0 = a[0] - b[0],\n      p1 = a[1] - b[1],\n      sinC = Math.sin(rad),\n      cosC = Math.cos(rad); //perform rotation and translate to correct position\n\n  out[0] = p0 * cosC - p1 * sinC + b[0];\n  out[1] = p0 * sinC + p1 * cosC + b[1];\n  return out;\n}\n/**\n * Get the angle between two 2D vectors\n * @param {ReadonlyVec2} a The first operand\n * @param {ReadonlyVec2} b The second operand\n * @returns {Number} The angle in radians\n */\n\nexport function angle(a, b) {\n  var x1 = a[0],\n      y1 = a[1],\n      x2 = b[0],\n      y2 = b[1],\n      // mag is the product of the magnitudes of a and b\n  mag = Math.sqrt(x1 * x1 + y1 * y1) * Math.sqrt(x2 * x2 + y2 * y2),\n      // mag &&.. short circuits if mag == 0\n  cosine = mag && (x1 * x2 + y1 * y2) / mag; // Math.min(Math.max(cosine, -1), 1) clamps the cosine between -1 and 1\n\n  return Math.acos(Math.min(Math.max(cosine, -1), 1));\n}\n/**\n * Set the components of a vec2 to zero\n *\n * @param {vec2} out the receiving vector\n * @returns {vec2} out\n */\n\nexport function zero(out) {\n  out[0] = 0.0;\n  out[1] = 0.0;\n  return out;\n}\n/**\n * Returns a string representation of a vector\n *\n * @param {ReadonlyVec2} a vector to represent as a string\n * @returns {String} string representation of the vector\n */\n\nexport function str(a) {\n  return \"vec2(\" + a[0] + \", \" + a[1] + \")\";\n}\n/**\n * Returns whether or not the vectors exactly have the same elements in the same position (when compared with ===)\n *\n * @param {ReadonlyVec2} a The first vector.\n * @param {ReadonlyVec2} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function exactEquals(a, b) {\n  return a[0] === b[0] && a[1] === b[1];\n}\n/**\n * Returns whether or not the vectors have approximately the same elements in the same position.\n *\n * @param {ReadonlyVec2} a The first vector.\n * @param {ReadonlyVec2} b The second vector.\n * @returns {Boolean} True if the vectors are equal, false otherwise.\n */\n\nexport function equals(a, b) {\n  var a0 = a[0],\n      a1 = a[1];\n  var b0 = b[0],\n      b1 = b[1];\n  return Math.abs(a0 - b0) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a0), Math.abs(b0)) && Math.abs(a1 - b1) <= glMatrix.EPSILON * Math.max(1.0, Math.abs(a1), Math.abs(b1));\n}\n/**\n * Alias for {@link vec2.length}\n * @function\n */\n\nexport var len = length;\n/**\n * Alias for {@link vec2.subtract}\n * @function\n */\n\nexport var sub = subtract;\n/**\n * Alias for {@link vec2.multiply}\n * @function\n */\n\nexport var mul = multiply;\n/**\n * Alias for {@link vec2.divide}\n * @function\n */\n\nexport var div = divide;\n/**\n * Alias for {@link vec2.distance}\n * @function\n */\n\nexport var dist = distance;\n/**\n * Alias for {@link vec2.squaredDistance}\n * @function\n */\n\nexport var sqrDist = squaredDistance;\n/**\n * Alias for {@link vec2.squaredLength}\n * @function\n */\n\nexport var sqrLen = squaredLength;\n/**\n * Perform some operation over an array of vec2s.\n *\n * @param {Array} a the array of vectors to iterate over\n * @param {Number} stride Number of elements between the start of each vec2. If 0 assumes tightly packed\n * @param {Number} offset Number of elements to skip at the beginning of the array\n * @param {Number} count Number of vec2s to iterate over. If 0 iterates over entire array\n * @param {Function} fn Function to call for each vector in the array\n * @param {Object} [arg] additional argument to pass to fn\n * @returns {Array} a\n * @function\n */\n\nexport var forEach = function () {\n  var vec = create();\n  return function (a, stride, offset, count, fn, arg) {\n    var i, l;\n\n    if (!stride) {\n      stride = 2;\n    }\n\n    if (!offset) {\n      offset = 0;\n    }\n\n    if (count) {\n      l = Math.min(count * stride + offset, a.length);\n    } else {\n      l = a.length;\n    }\n\n    for (i = offset; i < l; i += stride) {\n      vec[0] = a[i];\n      vec[1] = a[i + 1];\n      fn(vec, vec, arg);\n      a[i] = vec[0];\n      a[i + 1] = vec[1];\n    }\n\n    return a;\n  };\n}();"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKO,IAAI,UAAU;AACd,IAAI,aAAa,OAAO,iBAAiB,cAAc,eAAe;AACtE,IAAI,SAAS,KAAK;AAOlB,SAAS,mBAAmB,MAAM;AACvC,eAAa;AACf;AACA,IAAI,SAAS,KAAK,KAAK;AAOhB,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI;AACb;AAWO,SAAS,OAAO,GAAG,GAAG;AAC3B,SAAO,KAAK,IAAI,IAAI,CAAC,KAAK,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AAC5E;AACA,IAAI,CAAC,KAAK,MAAO,MAAK,QAAQ,WAAY;AACxC,MAAI,IAAI,GACJ,IAAI,UAAU;AAElB,SAAO,KAAK;AACV,SAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACjC;AAEA,SAAO,KAAK,KAAK,CAAC;AACpB;;;ACjDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYO,SAAS,SAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAAS,MAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AASO,SAAS,KAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAAS,SAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAWO,SAAS,WAAW,KAAK,KAAK,KAAK,KAAK;AAC7C,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAYO,SAAS,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAC3C,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAAS,UAAU,KAAK,GAAG;AAGhC,MAAI,QAAQ,GAAG;AACb,QAAI,KAAK,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI;AAAA,EACX,OAAO;AACL,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AAAA,EACd;AAEA,SAAO;AACT;AASO,SAAS,OAAO,KAAK,GAAG;AAC7B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AAEZ,MAAI,MAAM,KAAK,KAAK,KAAK;AAEzB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,QAAM,IAAM;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,KAAK;AACd,SAAO;AACT;AASO,SAAS,QAAQ,KAAK,GAAG;AAE9B,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAAS,YAAY,GAAG;AAC7B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AAUO,SAAS,SAAS,KAAK,GAAG,GAAG;AAClC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,SAAO;AACT;AAUO,SAAS,OAAO,KAAK,GAAG,KAAK;AAClC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI,KAAK,IAAI,KAAK;AACvB,MAAI,CAAC,IAAI,KAAK,IAAI,KAAK;AACvB,MAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK;AACxB,SAAO;AACT;AAUO,SAAS,MAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,SAAO;AACT;AAaO,SAAS,aAAa,KAAK,KAAK;AACrC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAAS,YAAY,KAAK,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAAS,IAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACpE;AAQO,SAAS,KAAK,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1C;AASO,SAAS,IAAI,GAAG,GAAG,GAAG,GAAG;AAC9B,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC;AACV,IAAE,CAAC,IAAI,EAAE,CAAC;AACV,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACxB,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAUO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAAS,SAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AASO,SAAS,YAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AACxE;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACxV;AAUO,SAAS,eAAe,KAAK,GAAG,GAAG;AACxC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAAS,qBAAqB,KAAK,GAAG,GAAGC,SAAO;AACrD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AAMO,IAAI,MAAM;AAMV,IAAI,MAAM;;;AC/ajB;AAAA;AAAA,aAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA;AAAA,oBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AA0BO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAASC,UAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAASC,YAAW,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI;AAC7C,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAcO,SAASC,KAAI,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI;AAC3C,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,KAAK,KAAK,KAAK;AAEzB,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,QAAM,IAAM;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,MAAI,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO;AACjC,SAAO;AACT;AAQO,SAASC,aAAY,GAAG;AAC7B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAC7B,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAC7B,SAAO;AACT;AAUO,SAASC,QAAO,KAAK,GAAG,KAAK;AAClC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI,KAAK,IAAI,KAAK;AACvB,MAAI,CAAC,IAAI,KAAK,IAAI,KAAK;AACvB,MAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK;AACxB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAAS,UAAU,KAAK,GAAG,GAAG;AACnC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAC7B,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAC7B,SAAO;AACT;AAaO,SAASC,cAAa,KAAK,KAAK;AACrC,MAAI,IAAI,KAAK,IAAI,GAAG,GAChB,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAASC,aAAY,KAAK,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAAS,gBAAgB,KAAK,GAAG;AACtC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAASC,KAAI,GAAG;AACrB,SAAO,WAAW,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACjG;AAQO,SAASC,MAAK,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AACzD;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,gBAAe,KAAK,GAAG,GAAG;AACxC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAASC,sBAAqB,KAAK,GAAG,GAAGR,SAAO;AACrD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AASO,SAASS,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAC1G;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAClgB;AAMO,IAAIC,OAAMb;AAMV,IAAIc,OAAMN;;;ACrejB;AAAA;AAAA,aAAAO;AAAA,EAAA,eAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,sBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,iBAAAC;AAAA;AAYO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAAS,SAAS,KAAK,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,EAAE;AACb,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAgBO,SAASC,YAAW,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACtE,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAiBO,SAASC,KAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACpE,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,UAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAASC,WAAU,KAAK,GAAG;AAEhC,MAAI,QAAQ,GAAG;AACb,QAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX,OAAO;AACL,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AAAA,EACd;AAEA,SAAO;AACT;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,CAAC,MAAM,MAAM,MAAM;AAC7B,MAAI,MAAM,MAAM,MAAM,MAAM;AAE5B,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAExC,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,QAAM,IAAM;AACZ,MAAI,CAAC,IAAI,MAAM;AACf,MAAI,CAAC,KAAK,CAAC,MAAM,MAAM,MAAM,OAAO;AACpC,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,IAAI,MAAM;AACf,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,KAAK,CAAC,MAAM,MAAM,MAAM,OAAO;AACpC,MAAI,CAAC,IAAI,MAAM;AACf,MAAI,CAAC,KAAK,CAAC,MAAM,MAAM,MAAM,OAAO;AACpC,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO;AACnC,SAAO;AACT;AASO,SAASC,SAAQ,KAAK,GAAG;AAC9B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3B,SAAO;AACT;AAQO,SAASC,aAAY,GAAG;AAC7B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,SAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,CAAC,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACnG;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,SAAO;AACT;AAUO,SAASC,WAAU,KAAK,GAAG,GAAG;AACnC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI,MAAM;AAC7B,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI,MAAM;AAC7B,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI,MAAM;AAC7B,SAAO;AACT;AAUO,SAASC,QAAO,KAAK,GAAG,KAAK;AAClC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,IAAI,KAAK,IAAI,GAAG,GAChB,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI,IAAI,MAAM,IAAI;AACvB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAaO,SAASC,iBAAgB,KAAK,GAAG;AACtC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAASC,cAAa,KAAK,KAAK;AACrC,MAAI,IAAI,KAAK,IAAI,GAAG,GAChB,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAaO,SAASC,aAAY,KAAK,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAAS,UAAU,KAAK,GAAG;AAChC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAAS,SAAS,KAAK,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,CAAC,IAAI,IAAI,KAAK;AAClB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,IAAI,KAAK;AAClB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,IAAI,KAAK;AAClB,SAAO;AACT;AAUO,SAAS,eAAe,KAAK,GAAG;AACrC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAE5B,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAE5E,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,QAAM,IAAM;AACZ,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,SAAO;AACT;AAUO,SAAS,WAAW,KAAK,OAAO,QAAQ;AAC7C,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AAC1I;AAQO,SAASC,MAAK,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACxE;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,gBAAe,KAAK,GAAG,GAAG;AACxC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAASC,sBAAqB,KAAK,GAAG,GAAGT,SAAO;AACrD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AASO,SAASU,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAC7J;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACjwB;AAMO,IAAIC,OAAMf;AAMV,IAAIgB,OAAMN;;;ACzwBjB;AAAA;AAAA,aAAAO;AAAA,EAAA,eAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA,sBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,qBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA;AAAA,aAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA,mBAAAC;AAAA,EAAA,iBAAAC;AAAA;AAYO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,EAAE;AAEpC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AAAA,EACZ;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,EAAE;AACpC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,SAAO;AACT;AAuBO,SAASC,YAAW,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACzG,MAAI,MAAM,IAAa,WAAW,EAAE;AACpC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAwBO,SAASC,KAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACvG,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAQO,SAASC,UAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AASO,SAASC,WAAU,KAAK,GAAG;AAEhC,MAAI,QAAQ,GAAG;AACb,QAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,QAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,QAAI,MAAM,EAAE,EAAE;AACd,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,EAAE;AACb,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,EAAE;AACb,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AAAA,EACZ,OAAO;AACL,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,EAAE;AACb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,EAAE;AACb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,CAAC;AACb,QAAI,EAAE,IAAI,EAAE,CAAC;AACb,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EAChB;AAEA,SAAO;AACT;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAE5B,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAE5E,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,QAAM,IAAM;AACZ,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,CAAC,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAC/C,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,MAAI,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAChD,SAAO;AACT;AASO,SAASC,SAAQ,KAAK,GAAG;AAC9B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AAClG,MAAI,CAAC,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACpG,MAAI,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AAClG,MAAI,CAAC,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACpG,MAAI,CAAC,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACpG,MAAI,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AAClG,MAAI,CAAC,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACpG,MAAI,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AAClG,MAAI,CAAC,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AAClG,MAAI,CAAC,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACpG,MAAI,EAAE,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACnG,MAAI,EAAE,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACrG,MAAI,EAAE,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACrG,MAAI,EAAE,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACnG,MAAI,EAAE,IAAI,EAAE,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACrG,MAAI,EAAE,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM;AACnG,SAAO;AACT;AAQO,SAASC,aAAY,GAAG;AAC7B,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAC5B,MAAI,MAAM,MAAM,MAAM,MAAM;AAE5B,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC3E;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AAEd,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,EAAE;AACT,OAAK,EAAE,EAAE;AACT,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,OAAK,EAAE,EAAE;AACT,OAAK,EAAE,EAAE;AACT,OAAK,EAAE,EAAE;AACT,OAAK,EAAE,EAAE;AACT,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,EAAE,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,SAAO;AACT;AAUO,SAASC,WAAU,KAAK,GAAG,GAAG;AACnC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AAEnB,MAAI,MAAM,KAAK;AACb,QAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AAC/C,QAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AAC/C,QAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;AAChD,QAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;AAAA,EAClD,OAAO;AACL,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,CAAC;AACT,UAAM,EAAE,EAAE;AACV,UAAM,EAAE,EAAE;AACV,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,EAAE;AAC5C,QAAI,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,EAAE;AAC5C,QAAI,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,EAAE;AAC5C,QAAI,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,EAAE,EAAE;AAAA,EAC9C;AAEA,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,MAAI,EAAE,IAAI,EAAE,EAAE;AACd,SAAO;AACT;AAWO,SAASC,QAAO,KAAK,GAAG,KAAK,MAAM;AACxC,MAAI,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC;AACd,MAAIC,OAAM,KAAK,MAAM,GAAG,GAAG,CAAC;AAC5B,MAAI,GAAG,GAAG;AACV,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AAEd,MAAIA,OAAe,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,EAAAA,OAAM,IAAIA;AACV,OAAKA;AACL,OAAKA;AACL,OAAKA;AACL,MAAI,KAAK,IAAI,GAAG;AAChB,MAAI,KAAK,IAAI,GAAG;AAChB,MAAI,IAAI;AACR,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,CAAC;AACT,QAAM,EAAE,EAAE;AACV,QAAM,EAAE,EAAE;AAEV,QAAM,IAAI,IAAI,IAAI;AAClB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI;AAClB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI,IAAI;AACtB,QAAM,IAAI,IAAI,IAAI;AAElB,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACvC,MAAI,EAAE,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AACxC,MAAI,EAAE,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AAExC,MAAI,MAAM,KAAK;AAEb,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EAChB;AAEA,SAAO;AACT;AAUO,SAAS,QAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE;AAEd,MAAI,MAAM,KAAK;AAEb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EAChB;AAGA,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1B,MAAI,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1B,SAAO;AACT;AAUO,SAAS,QAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE;AAEd,MAAI,MAAM,KAAK;AAEb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EAChB;AAGA,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1B,MAAI,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1B,SAAO;AACT;AAUO,SAAS,QAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AACb,MAAI,MAAM,EAAE,CAAC;AAEb,MAAI,MAAM,KAAK;AAEb,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AACd,QAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EAChB;AAGA,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,MAAI,CAAC,IAAI,MAAM,IAAI,MAAM;AACzB,SAAO;AACT;AAaO,SAASC,iBAAgB,KAAK,GAAG;AACtC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAaO,SAASC,aAAY,KAAK,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAcO,SAASC,cAAa,KAAK,KAAK,MAAM;AAC3C,MAAI,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC;AACd,MAAIH,OAAM,KAAK,MAAM,GAAG,GAAG,CAAC;AAC5B,MAAI,GAAG,GAAG;AAEV,MAAIA,OAAe,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,EAAAA,OAAM,IAAIA;AACV,OAAKA;AACL,OAAKA;AACL,OAAKA;AACL,MAAI,KAAK,IAAI,GAAG;AAChB,MAAI,KAAK,IAAI,GAAG;AAChB,MAAI,IAAI;AAER,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI;AACrB,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI;AACrB,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AACzB,MAAI,EAAE,IAAI,IAAI,IAAI,IAAI;AACtB,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAaO,SAAS,cAAc,KAAK,KAAK;AACtC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AAEpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAaO,SAAS,cAAc,KAAK,KAAK;AACtC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AAEpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAaO,SAAS,cAAc,KAAK,KAAK;AACtC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AAEpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,CAAC;AACV,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAiBO,SAAS,wBAAwB,KAAK,GAAG,GAAG;AAEjD,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,CAAC,IAAI,KAAK,KAAK;AACnB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK,KAAK;AACnB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,EAAE,IAAI,KAAK,KAAK;AACpB,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AASO,SAAS,UAAU,KAAK,GAAG;AAChC,MAAI,cAAc,IAAa,WAAW,CAAC;AAC3C,MAAI,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,YAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAEnD,MAAI,YAAY,GAAG;AACjB,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI;AAC/D,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI;AAC/D,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI;AAAA,EACjE,OAAO;AACL,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AAC3D,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AAC3D,gBAAY,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AAAA,EAC7D;AAEA,0BAAwB,KAAK,GAAG,WAAW;AAC3C,SAAO;AACT;AAWO,SAAS,eAAe,KAAK,KAAK;AACvC,MAAI,CAAC,IAAI,IAAI,EAAE;AACf,MAAI,CAAC,IAAI,IAAI,EAAE;AACf,MAAI,CAAC,IAAI,IAAI,EAAE;AACf,SAAO;AACT;AAYO,SAAS,WAAW,KAAK,KAAK;AACnC,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,MAAM,IAAI,EAAE;AAChB,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG;AACjC,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG;AACjC,MAAI,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG;AACjC,SAAO;AACT;AAWO,SAAS,YAAY,KAAK,KAAK;AACpC,MAAI,UAAU,IAAa,WAAW,CAAC;AACvC,aAAW,SAAS,GAAG;AACvB,MAAI,MAAM,IAAI,QAAQ,CAAC;AACvB,MAAI,MAAM,IAAI,QAAQ,CAAC;AACvB,MAAI,MAAM,IAAI,QAAQ,CAAC;AACvB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,CAAC,IAAI;AACpB,MAAI,OAAO,IAAI,EAAE,IAAI;AACrB,MAAI,QAAQ,OAAO,OAAO;AAC1B,MAAI,IAAI;AAER,MAAI,QAAQ,GAAG;AACb,QAAI,KAAK,KAAK,QAAQ,CAAG,IAAI;AAC7B,QAAI,CAAC,IAAI,OAAO;AAChB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AAAA,EAC3B,WAAW,OAAO,QAAQ,OAAO,MAAM;AACrC,QAAI,KAAK,KAAK,IAAM,OAAO,OAAO,IAAI,IAAI;AAC1C,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,IAAI,OAAO;AAChB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AAAA,EAC3B,WAAW,OAAO,MAAM;AACtB,QAAI,KAAK,KAAK,IAAM,OAAO,OAAO,IAAI,IAAI;AAC1C,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,IAAI,OAAO;AAChB,QAAI,CAAC,KAAK,OAAO,QAAQ;AAAA,EAC3B,OAAO;AACL,QAAI,KAAK,KAAK,IAAM,OAAO,OAAO,IAAI,IAAI;AAC1C,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,KAAK,OAAO,QAAQ;AACzB,QAAI,CAAC,IAAI,OAAO;AAAA,EAClB;AAEA,SAAO;AACT;AAmBO,SAAS,6BAA6B,KAAK,GAAG,GAAG,GAAG;AAEzD,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,KAAK,KAAK,KAAK,OAAO;AAC3B,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,CAAC,KAAK,KAAK,KAAK,OAAO;AAC3B,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,CAAC,KAAK,KAAK,MAAM;AACrB,MAAI,EAAE,KAAK,KAAK,KAAK,OAAO;AAC5B,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI,EAAE,CAAC;AACb,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAsBO,SAAS,mCAAmC,KAAK,GAAG,GAAG,GAAG,GAAG;AAElE,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,QAAQ,KAAK,KAAK,OAAO;AAC7B,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,QAAQ,KAAK,KAAK,OAAO;AAC7B,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,SAAS,KAAK,KAAK,OAAO;AAC9B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,EAAE,CAAC,IAAI,MAAM,OAAO,KAAK,OAAO,KAAK,OAAO;AACtD,MAAI,EAAE,IAAI,EAAE,CAAC,IAAI,MAAM,OAAO,KAAK,OAAO,KAAK,OAAO;AACtD,MAAI,EAAE,IAAI,EAAE,CAAC,IAAI,MAAM,OAAO,KAAK,OAAO,KAAK,QAAQ;AACvD,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAUO,SAASI,UAAS,KAAK,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,CAAC,IAAI,IAAI,KAAK;AAClB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,IAAI,KAAK;AAClB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,EAAE,IAAI,IAAI,KAAK;AACnB,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAcO,SAAS,QAAQ,KAAK,MAAM,OAAO,QAAQ,KAAK,MAAM,KAAK;AAChE,MAAI,KAAK,KAAK,QAAQ;AACtB,MAAI,KAAK,KAAK,MAAM;AACpB,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,CAAC,IAAI,OAAO,IAAI;AACpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,OAAO,IAAI;AACpB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,KAAK,QAAQ,QAAQ;AAC1B,MAAI,CAAC,KAAK,MAAM,UAAU;AAC1B,MAAI,EAAE,KAAK,MAAM,QAAQ;AACzB,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,MAAM,OAAO,IAAI;AAC3B,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAeO,SAAS,cAAc,KAAK,MAAM,QAAQ,MAAM,KAAK;AAC1D,MAAI,IAAI,IAAM,KAAK,IAAI,OAAO,CAAC,GAC3B;AACJ,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AAEV,MAAI,OAAO,QAAQ,QAAQ,UAAU;AACnC,SAAK,KAAK,OAAO;AACjB,QAAI,EAAE,KAAK,MAAM,QAAQ;AACzB,QAAI,EAAE,IAAI,IAAI,MAAM,OAAO;AAAA,EAC7B,OAAO;AACL,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI,KAAK;AAAA,EACjB;AAEA,SAAO;AACT;AAMO,IAAI,cAAc;AAelB,SAAS,cAAc,KAAK,MAAM,QAAQ,MAAM,KAAK;AAC1D,MAAI,IAAI,IAAM,KAAK,IAAI,OAAO,CAAC,GAC3B;AACJ,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AAEV,MAAI,OAAO,QAAQ,QAAQ,UAAU;AACnC,SAAK,KAAK,OAAO;AACjB,QAAI,EAAE,IAAI,MAAM;AAChB,QAAI,EAAE,IAAI,MAAM,OAAO;AAAA,EACzB,OAAO;AACL,QAAI,EAAE,IAAI;AACV,QAAI,EAAE,IAAI,CAAC;AAAA,EACb;AAEA,SAAO;AACT;AAaO,SAAS,2BAA2B,KAAK,KAAK,MAAM,KAAK;AAC9D,MAAI,QAAQ,KAAK,IAAI,IAAI,YAAY,KAAK,KAAK,GAAK;AACpD,MAAI,UAAU,KAAK,IAAI,IAAI,cAAc,KAAK,KAAK,GAAK;AACxD,MAAI,UAAU,KAAK,IAAI,IAAI,cAAc,KAAK,KAAK,GAAK;AACxD,MAAI,WAAW,KAAK,IAAI,IAAI,eAAe,KAAK,KAAK,GAAK;AAC1D,MAAI,SAAS,KAAO,UAAU;AAC9B,MAAI,SAAS,KAAO,QAAQ;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,GAAG,UAAU,YAAY,SAAS;AAC3C,MAAI,CAAC,KAAK,QAAQ,WAAW,SAAS;AACtC,MAAI,EAAE,IAAI,OAAO,OAAO;AACxB,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,MAAM,QAAQ,OAAO;AAC/B,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAgBO,SAAS,QAAQ,KAAK,MAAM,OAAO,QAAQ,KAAK,MAAM,KAAK;AAChE,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,KAAK,KAAK,SAAS;AACvB,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI,IAAI;AACd,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,KAAK,OAAO,SAAS;AAC3B,MAAI,EAAE,KAAK,MAAM,UAAU;AAC3B,MAAI,EAAE,KAAK,MAAM,QAAQ;AACzB,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAMO,IAAI,QAAQ;AAgBZ,SAAS,QAAQ,KAAK,MAAM,OAAO,QAAQ,KAAK,MAAM,KAAK;AAChE,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,KAAK,KAAK,SAAS;AACvB,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,KAAK,OAAO,SAAS;AAC3B,MAAI,EAAE,KAAK,MAAM,UAAU;AAC3B,MAAI,EAAE,IAAI,OAAO;AACjB,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAYO,SAAS,OAAO,KAAK,KAAK,QAAQ,IAAI;AAC3C,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIJ;AACxC,MAAI,OAAO,IAAI,CAAC;AAChB,MAAI,OAAO,IAAI,CAAC;AAChB,MAAI,OAAO,IAAI,CAAC;AAChB,MAAI,MAAM,GAAG,CAAC;AACd,MAAI,MAAM,GAAG,CAAC;AACd,MAAI,MAAM,GAAG,CAAC;AACd,MAAI,UAAU,OAAO,CAAC;AACtB,MAAI,UAAU,OAAO,CAAC;AACtB,MAAI,UAAU,OAAO,CAAC;AAEtB,MAAI,KAAK,IAAI,OAAO,OAAO,IAAa,WAAW,KAAK,IAAI,OAAO,OAAO,IAAa,WAAW,KAAK,IAAI,OAAO,OAAO,IAAa,SAAS;AAC7I,WAAOT,UAAS,GAAG;AAAA,EACrB;AAEA,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,EAAAS,OAAM,IAAI,KAAK,MAAM,IAAI,IAAI,EAAE;AAC/B,QAAMA;AACN,QAAMA;AACN,QAAMA;AACN,OAAK,MAAM,KAAK,MAAM;AACtB,OAAK,MAAM,KAAK,MAAM;AACtB,OAAK,MAAM,KAAK,MAAM;AACtB,EAAAA,OAAM,KAAK,MAAM,IAAI,IAAI,EAAE;AAE3B,MAAI,CAACA,MAAK;AACR,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AACL,IAAAA,OAAM,IAAIA;AACV,UAAMA;AACN,UAAMA;AACN,UAAMA;AAAA,EACR;AAEA,OAAK,KAAK,KAAK,KAAK;AACpB,OAAK,KAAK,KAAK,KAAK;AACpB,OAAK,KAAK,KAAK,KAAK;AACpB,EAAAA,OAAM,KAAK,MAAM,IAAI,IAAI,EAAE;AAE3B,MAAI,CAACA,MAAK;AACR,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AACL,IAAAA,OAAM,IAAIA;AACV,UAAMA;AACN,UAAMA;AACN,UAAMA;AAAA,EACR;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI,EAAE,KAAK,OAAO,KAAK,OAAO,KAAK;AACzC,MAAI,EAAE,IAAI,EAAE,KAAK,OAAO,KAAK,OAAO,KAAK;AACzC,MAAI,EAAE,IAAI,EAAE,KAAK,OAAO,KAAK,OAAO,KAAK;AACzC,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAWO,SAAS,SAAS,KAAK,KAAK,QAAQ,IAAI;AAC7C,MAAI,OAAO,IAAI,CAAC,GACZ,OAAO,IAAI,CAAC,GACZ,OAAO,IAAI,CAAC,GACZ,MAAM,GAAG,CAAC,GACV,MAAM,GAAG,CAAC,GACV,MAAM,GAAG,CAAC;AACd,MAAI,KAAK,OAAO,OAAO,CAAC,GACpB,KAAK,OAAO,OAAO,CAAC,GACpB,KAAK,OAAO,OAAO,CAAC;AACxB,MAAIA,OAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAEnC,MAAIA,OAAM,GAAG;AACX,IAAAA,OAAM,IAAI,KAAK,KAAKA,IAAG;AACvB,UAAMA;AACN,UAAMA;AACN,UAAMA;AAAA,EACR;AAEA,MAAI,KAAK,MAAM,KAAK,MAAM,IACtB,KAAK,MAAM,KAAK,MAAM,IACtB,KAAK,MAAM,KAAK,MAAM;AAC1B,EAAAA,OAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAE/B,MAAIA,OAAM,GAAG;AACX,IAAAA,OAAM,IAAI,KAAK,KAAKA,IAAG;AACvB,UAAMA;AACN,UAAMA;AACN,UAAMA;AAAA,EACR;AAEA,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,MAAI,EAAE,IAAI;AACV,SAAO;AACT;AAQO,SAASK,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI;AAClP;AAQO,SAASC,MAAK,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AACxH;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE;AACtB,SAAO;AACT;AAUO,SAASC,gBAAe,KAAK,GAAG,GAAG;AACxC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI;AAClB,SAAO;AACT;AAWO,SAASC,sBAAqB,KAAK,GAAG,GAAGZ,SAAO;AACrD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,MAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAIA;AAC1B,SAAO;AACT;AASO,SAASa,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE;AAChS;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,MAAI,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AACd,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,MAAM,GAAG,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAC52C;AAMO,IAAIC,OAAMjB;AAMV,IAAIkB,OAAMN;;;ACr3DjB;AAAA;AAAA,aAAAO;AAAA,EAAA;AAAA,eAAAC;AAAA,EAAA;AAAA,cAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,oBAAAC;AAAA,EAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA;AAAA,aAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA;AAAA,gBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA;AAAA,eAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,WAAAC;AAAA;;;ACAA;AAAA;AAAA,aAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA,oBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA;AAAA,eAAAC;AAAA,EAAA;AAAA,aAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,aAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAYO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAAS,OAAO,GAAG;AACxB,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,SAAO,KAAK,MAAM,GAAG,GAAG,CAAC;AAC3B;AAUO,SAASC,YAAW,GAAG,GAAG,GAAG;AAClC,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAWO,SAASC,KAAI,KAAK,GAAG,GAAG,GAAG;AAChC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAAS,OAAO,KAAK,GAAG,GAAG;AAChC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AASO,SAAS,KAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,SAAO;AACT;AASO,SAAS,MAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AAUO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AASO,SAAS,MAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAAS,YAAY,KAAK,GAAG,GAAGA,SAAO;AAC5C,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AASO,SAAS,SAAS,GAAG,GAAG;AAC7B,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,KAAK,MAAM,GAAG,GAAG,CAAC;AAC3B;AASO,SAAS,gBAAgB,GAAG,GAAG;AACpC,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B;AAQO,SAAS,cAAc,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B;AASO,SAAS,OAAO,KAAK,GAAG;AAC7B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,SAAO;AACT;AASO,SAAS,QAAQ,KAAK,GAAG;AAC9B,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,SAAO;AACT;AASO,SAAS,UAAU,KAAK,GAAG;AAChC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAIC,OAAM,IAAI,IAAI,IAAI,IAAI,IAAI;AAE9B,MAAIA,OAAM,GAAG;AAEX,IAAAA,OAAM,IAAI,KAAK,KAAKA,IAAG;AAAA,EACzB;AAEA,MAAI,CAAC,IAAI,EAAE,CAAC,IAAIA;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAIA;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAIA;AAChB,SAAO;AACT;AASO,SAAS,IAAI,GAAG,GAAG;AACxB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAUO,SAAS,MAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,SAAO;AACT;AAWO,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG;AACjC,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,SAAO;AACT;AAaO,SAAS,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AAC1C,MAAI,eAAe,IAAI;AACvB,MAAI,UAAU,gBAAgB,IAAI,IAAI,KAAK;AAC3C,MAAI,UAAU,gBAAgB,IAAI,KAAK;AACvC,MAAI,UAAU,gBAAgB,IAAI;AAClC,MAAI,UAAU,gBAAgB,IAAI,IAAI;AACtC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,SAAO;AACT;AAaO,SAAS,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACzC,MAAI,gBAAgB,IAAI;AACxB,MAAI,wBAAwB,gBAAgB;AAC5C,MAAI,eAAe,IAAI;AACvB,MAAI,UAAU,wBAAwB;AACtC,MAAI,UAAU,IAAI,IAAI;AACtB,MAAI,UAAU,IAAI,eAAe;AACjC,MAAI,UAAU,eAAe;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,IAAI;AACnE,SAAO;AACT;AASO,SAAS,OAAO,KAAKD,SAAO;AACjC,EAAAA,UAAQA,WAAS;AACjB,MAAI,IAAa,OAAO,IAAI,IAAM,KAAK;AACvC,MAAI,IAAa,OAAO,IAAI,IAAM;AAClC,MAAI,SAAS,KAAK,KAAK,IAAM,IAAI,CAAC,IAAIA;AACtC,MAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AACvB,MAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AACvB,MAAI,CAAC,IAAI,IAAIA;AACb,SAAO;AACT;AAWO,SAAS,cAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;AAC9C,MAAI,KAAK;AACT,MAAI,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK;AACpD,MAAI,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK;AACpD,MAAI,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,KAAK;AACrD,SAAO;AACT;AAUO,SAAS,cAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AACtC,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AACtC,MAAI,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AACtC,SAAO;AACT;AAWO,SAAS,cAAc,KAAK,GAAG,GAAG;AAEvC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AAGX,MAAI,MAAM,KAAK,IAAI,KAAK,GACpB,MAAM,KAAK,IAAI,KAAK,GACpB,MAAM,KAAK,IAAI,KAAK;AAExB,MAAI,OAAO,KAAK,MAAM,KAAK,KACvB,OAAO,KAAK,MAAM,KAAK,KACvB,OAAO,KAAK,MAAM,KAAK;AAE3B,MAAI,KAAK,KAAK;AACd,SAAO;AACP,SAAO;AACP,SAAO;AAEP,UAAQ;AACR,UAAQ;AACR,UAAQ;AAER,MAAI,CAAC,IAAI,IAAI,MAAM;AACnB,MAAI,CAAC,IAAI,IAAI,MAAM;AACnB,MAAI,CAAC,IAAI,IAAI,MAAM;AACnB,SAAO;AACT;AAUO,SAASE,SAAQ,KAAK,GAAG,GAAG,KAAK;AACtC,MAAI,IAAI,CAAC,GACL,IAAI,CAAC;AAET,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAEjB,IAAE,CAAC,IAAI,EAAE,CAAC;AACV,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AACjD,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AAEjD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,GAAG,KAAK;AACtC,MAAI,IAAI,CAAC,GACL,IAAI,CAAC;AAET,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAEjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AACjD,IAAE,CAAC,IAAI,EAAE,CAAC;AACV,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AAEjD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,GAAG,KAAK;AACtC,MAAI,IAAI,CAAC,GACL,IAAI,CAAC;AAET,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAEjB,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AACjD,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AACjD,IAAE,CAAC,IAAI,EAAE,CAAC;AAEV,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAQO,SAAS,MAAM,GAAG,GAAG;AAC1B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAC5C,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAC5C,MAAM,OAAO,MACb,SAAS,OAAO,IAAI,GAAG,CAAC,IAAI;AAChC,SAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE,GAAG,CAAC,CAAC;AACpD;AAQO,SAAS,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACtD;AASO,SAASC,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AACvD;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACnQ;AAMO,IAAIC,OAAMV;AAMV,IAAIW,OAAMV;AAMV,IAAI,MAAM;AAMV,IAAI,OAAO;AAMX,IAAI,UAAU;AAMd,IAAI,MAAM;AAMV,IAAI,SAAS;AAcb,IAAI,UAAU,WAAY;AAC/B,MAAI,MAAMP,QAAO;AACjB,SAAO,SAAU,GAAG,QAAQ,QAAQ,OAAO,IAAI,KAAK;AAClD,QAAI,GAAG;AAEP,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,OAAO;AACT,UAAI,KAAK,IAAI,QAAQ,SAAS,QAAQ,EAAE,MAAM;AAAA,IAChD,OAAO;AACL,UAAI,EAAE;AAAA,IACR;AAEA,SAAK,IAAI,QAAQ,IAAI,GAAG,KAAK,QAAQ;AACnC,UAAI,CAAC,IAAI,EAAE,CAAC;AACZ,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,SAAG,KAAK,KAAK,GAAG;AAChB,QAAE,CAAC,IAAI,IAAI,CAAC;AACZ,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAChB,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAClB;AAEA,WAAO;AAAA,EACT;AACF,EAAE;;;AClxBF;AAAA;AAAA,aAAAkB;AAAA,EAAA,YAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,YAAAC;AAAA;AAYO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAWO,SAASC,YAAW,GAAG,GAAG,GAAG,GAAG;AACrC,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAYO,SAASC,KAAI,KAAK,GAAG,GAAG,GAAG,GAAG;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,QAAO,KAAK,GAAG,GAAG;AAChC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,SAAO;AACT;AASO,SAASC,OAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AASO,SAASC,OAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAASC,aAAY,KAAK,GAAG,GAAGD,SAAO;AAC5C,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AASO,SAASE,UAAS,GAAG,GAAG;AAC7B,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;AAC9B;AASO,SAASC,iBAAgB,GAAG,GAAG;AACpC,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC;AAQO,SAASC,QAAO,GAAG;AACxB,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,SAAO,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;AAC9B;AAQO,SAASC,eAAc,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACrC;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,SAAO;AACT;AASO,SAASC,SAAQ,KAAK,GAAG;AAC9B,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,SAAO;AACT;AASO,SAASC,WAAU,KAAK,GAAG;AAChC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAIC,OAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAEtC,MAAIA,OAAM,GAAG;AACX,IAAAA,OAAM,IAAI,KAAK,KAAKA,IAAG;AAAA,EACzB;AAEA,MAAI,CAAC,IAAI,IAAIA;AACb,MAAI,CAAC,IAAI,IAAIA;AACb,MAAI,CAAC,IAAI,IAAIA;AACb,MAAI,CAAC,IAAI,IAAIA;AACb,SAAO;AACT;AASO,SAASC,KAAI,GAAG,GAAG;AACxB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC7D;AAWO,SAASC,OAAM,KAAK,GAAG,GAAG,GAAG;AAClC,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAC5B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAC5B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAC5B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAC5B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAC5B,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI;AAChC,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B,MAAI,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI;AAChC,SAAO;AACT;AAWO,SAASC,MAAK,KAAK,GAAG,GAAG,GAAG;AACjC,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,SAAO;AACT;AASO,SAASC,QAAO,KAAKb,SAAO;AACjC,EAAAA,UAAQA,WAAS;AAIjB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,IAAI;AAER,KAAG;AACD,SAAc,OAAO,IAAI,IAAI;AAC7B,SAAc,OAAO,IAAI,IAAI;AAC7B,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB,SAAS,MAAM;AAEf,KAAG;AACD,SAAc,OAAO,IAAI,IAAI;AAC7B,SAAc,OAAO,IAAI,IAAI;AAC7B,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB,SAAS,MAAM;AAEf,MAAI,IAAI,KAAK,MAAM,IAAI,MAAM,EAAE;AAC/B,MAAI,CAAC,IAAIA,UAAQ;AACjB,MAAI,CAAC,IAAIA,UAAQ;AACjB,MAAI,CAAC,IAAIA,UAAQ,KAAK;AACtB,MAAI,CAAC,IAAIA,UAAQ,KAAK;AACtB,SAAO;AACT;AAUO,SAASc,eAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI;AAClD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI;AAClD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI;AACnD,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI;AACnD,SAAO;AACT;AAUO,SAASC,eAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AAEZ,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAChC,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAChC,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAChC,MAAI,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK;AAEjC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAC/C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAC/C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAC/C,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAASC,MAAK,KAAK;AACxB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACpE;AASO,SAASC,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AACxE;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACxV;AAMO,IAAIC,OAAM5B;AAMV,IAAI6B,OAAM5B;AAMV,IAAI6B,OAAM5B;AAMV,IAAI6B,QAAOrB;AAMX,IAAIsB,WAAUrB;AAMd,IAAIM,OAAML;AAMV,IAAIqB,UAASpB;AAcb,IAAIqB,WAAU,WAAY;AAC/B,MAAI,MAAMxC,QAAO;AACjB,SAAO,SAAU,GAAG,QAAQ,QAAQ,OAAO,IAAI,KAAK;AAClD,QAAI,GAAG;AAEP,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,OAAO;AACT,UAAI,KAAK,IAAI,QAAQ,SAAS,QAAQ,EAAE,MAAM;AAAA,IAChD,OAAO;AACL,UAAI,EAAE;AAAA,IACR;AAEA,SAAK,IAAI,QAAQ,IAAI,GAAG,KAAK,QAAQ;AACnC,UAAI,CAAC,IAAI,EAAE,CAAC;AACZ,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,SAAG,KAAK,KAAK,GAAG;AAChB,QAAE,CAAC,IAAI,IAAI,CAAC;AACZ,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAChB,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAChB,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAClB;AAEA,WAAO;AAAA,EACT;AACF,EAAE;;;AFvoBK,SAASyC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,UAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAWO,SAAS,aAAa,KAAK,MAAM,KAAK;AAC3C,QAAM,MAAM;AACZ,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,CAAC,IAAI,IAAI,KAAK,CAAC;AACnB,MAAI,CAAC,IAAI,IAAI,KAAK,CAAC;AACnB,MAAI,CAAC,IAAI,IAAI,KAAK,CAAC;AACnB,MAAI,CAAC,IAAI,KAAK,IAAI,GAAG;AACrB,SAAO;AACT;AAeO,SAAS,aAAa,UAAU,GAAG;AACxC,MAAI,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI;AAC5B,MAAI,IAAI,KAAK,IAAI,MAAM,CAAG;AAE1B,MAAI,IAAa,SAAS;AACxB,aAAS,CAAC,IAAI,EAAE,CAAC,IAAI;AACrB,aAAS,CAAC,IAAI,EAAE,CAAC,IAAI;AACrB,aAAS,CAAC,IAAI,EAAE,CAAC,IAAI;AAAA,EACvB,OAAO;AAEL,aAAS,CAAC,IAAI;AACd,aAAS,CAAC,IAAI;AACd,aAAS,CAAC,IAAI;AAAA,EAChB;AAEA,SAAO;AACT;AASO,SAAS,SAAS,GAAG,GAAG;AAC7B,MAAI,aAAaC,KAAI,GAAG,CAAC;AACzB,SAAO,KAAK,KAAK,IAAI,aAAa,aAAa,CAAC;AAClD;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,SAAO;AACP,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,KAAK,IAAI,GAAG,GACjB,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,SAAO;AACP,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,KAAK,IAAI,GAAG,GACjB,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,SAAO;AACP,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,KAAK,IAAI,GAAG,GACjB,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK;AACxB,SAAO;AACT;AAWO,SAAS,WAAW,KAAK,GAAG;AACjC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,IAAM,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AACxD,SAAO;AACT;AASO,SAAS,IAAI,KAAK,GAAG;AAC1B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AACvC,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI;AACvC,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC;AACxB,SAAO;AACT;AASO,SAAS,GAAG,KAAK,GAAG;AACzB,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AACvC,MAAI,IAAI,IAAI,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,IAAI;AACvC,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,IAAI;AACb,MAAI,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AACrD,SAAO;AACT;AAUO,SAAS,IAAI,KAAK,GAAG,GAAG;AAC7B,KAAG,KAAK,CAAC;AACT,EAAAC,OAAM,KAAK,KAAK,CAAC;AACjB,MAAI,KAAK,GAAG;AACZ,SAAO;AACT;AAWO,SAAS,MAAM,KAAK,GAAG,GAAG,GAAG;AAGlC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,OAAO,OAAO,OAAO,QAAQ;AAEjC,UAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAE3C,MAAI,QAAQ,GAAK;AACf,YAAQ,CAAC;AACT,SAAK,CAAC;AACN,SAAK,CAAC;AACN,SAAK,CAAC;AACN,SAAK,CAAC;AAAA,EACR;AAGA,MAAI,IAAM,QAAiB,SAAS;AAElC,YAAQ,KAAK,KAAK,KAAK;AACvB,YAAQ,KAAK,IAAI,KAAK;AACtB,aAAS,KAAK,KAAK,IAAM,KAAK,KAAK,IAAI;AACvC,aAAS,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,EACjC,OAAO;AAGL,aAAS,IAAM;AACf,aAAS;AAAA,EACX;AAGA,MAAI,CAAC,IAAI,SAAS,KAAK,SAAS;AAChC,MAAI,CAAC,IAAI,SAAS,KAAK,SAAS;AAChC,MAAI,CAAC,IAAI,SAAS,KAAK,SAAS;AAChC,MAAI,CAAC,IAAI,SAAS,KAAK,SAAS;AAChC,SAAO;AACT;AAQO,SAASC,QAAO,KAAK;AAG1B,MAAI,KAAc,OAAO;AACzB,MAAI,KAAc,OAAO;AACzB,MAAI,KAAc,OAAO;AACzB,MAAI,eAAe,KAAK,KAAK,IAAI,EAAE;AACnC,MAAI,SAAS,KAAK,KAAK,EAAE;AACzB,MAAI,CAAC,IAAI,eAAe,KAAK,IAAI,IAAM,KAAK,KAAK,EAAE;AACnD,MAAI,CAAC,IAAI,eAAe,KAAK,IAAI,IAAM,KAAK,KAAK,EAAE;AACnD,MAAI,CAAC,IAAI,SAAS,KAAK,IAAI,IAAM,KAAK,KAAK,EAAE;AAC7C,MAAI,CAAC,IAAI,SAAS,KAAK,IAAI,IAAM,KAAK,KAAK,EAAE;AAC7C,SAAO;AACT;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAIP,OAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC7C,MAAI,SAASA,OAAM,IAAMA,OAAM;AAE/B,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,CAAC,KAAK;AACf,MAAI,CAAC,IAAI,KAAK;AACd,SAAO;AACT;AAUO,SAAS,UAAU,KAAK,GAAG;AAChC,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAaO,SAAS,SAAS,KAAK,GAAG;AAG/B,MAAI,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9B,MAAI;AAEJ,MAAI,SAAS,GAAK;AAEhB,YAAQ,KAAK,KAAK,SAAS,CAAG;AAE9B,QAAI,CAAC,IAAI,MAAM;AACf,YAAQ,MAAM;AAEd,QAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACzB,QAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACzB,QAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAAA,EAC3B,OAAO;AAEL,QAAI,IAAI;AACR,QAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAG,KAAI;AACrB,QAAI,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,EAAG,KAAI;AAC7B,QAAI,KAAK,IAAI,KAAK;AAClB,QAAI,KAAK,IAAI,KAAK;AAClB,YAAQ,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAG;AAClE,QAAI,CAAC,IAAI,MAAM;AACf,YAAQ,MAAM;AACd,QAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK;AACzC,QAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK;AACzC,QAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK;AAAA,EAC3C;AAEA,SAAO;AACT;AAYO,SAAS,UAAU,KAAK,GAAG,GAAG,GAAG;AACtC,MAAI,YAAY,MAAM,KAAK,KAAK;AAChC,OAAK;AACL,OAAK;AACL,OAAK;AACL,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,KAAK,KAAK,IAAI,CAAC;AACnB,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,SAAO;AACT;AAQO,SAASQ,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACpE;AASO,IAAIC,SAAaA;AAYjB,IAAIC,cAAkBA;AAUtB,IAAIC,QAAYA;AAahB,IAAIC,OAAWA;AAWf,IAAIC,OAAWA;AAMf,IAAIC,OAAMb;AAWV,IAAII,SAAaA;AAUjB,IAAIL,OAAWA;AAYf,IAAIe,QAAYA;AAQhB,IAAIC,UAAcA;AAMlB,IAAIC,OAAMD;AASV,IAAIE,iBAAqBA;AAMzB,IAAIC,UAASD;AAUb,IAAIE,aAAiBA;AASrB,IAAIC,eAAmBA;AASvB,IAAIC,UAAcA;AAalB,IAAI,aAAa,WAAY;AAClC,MAAI,UAAexB,QAAO;AAC1B,MAAI,YAAiBY,YAAW,GAAG,GAAG,CAAC;AACvC,MAAI,YAAiBA,YAAW,GAAG,GAAG,CAAC;AACvC,SAAO,SAAU,KAAK,GAAG,GAAG;AAC1B,QAAIV,OAAW,IAAI,GAAG,CAAC;AAEvB,QAAIA,OAAM,WAAW;AACnB,MAAK,MAAM,SAAS,WAAW,CAAC;AAChC,UAAS,IAAI,OAAO,IAAI,KAAU,CAAK,MAAM,SAAS,WAAW,CAAC;AAClE,MAAK,UAAU,SAAS,OAAO;AAC/B,mBAAa,KAAK,SAAS,KAAK,EAAE;AAClC,aAAO;AAAA,IACT,WAAWA,OAAM,UAAU;AACzB,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,UAAI,CAAC,IAAI;AACT,aAAO;AAAA,IACT,OAAO;AACL,MAAK,MAAM,SAAS,GAAG,CAAC;AACxB,UAAI,CAAC,IAAI,QAAQ,CAAC;AAClB,UAAI,CAAC,IAAI,QAAQ,CAAC;AAClB,UAAI,CAAC,IAAI,QAAQ,CAAC;AAClB,UAAI,CAAC,IAAI,IAAIA;AACb,aAAOoB,WAAU,KAAK,GAAG;AAAA,IAC3B;AAAA,EACF;AACF,EAAE;AAaK,IAAI,SAAS,WAAY;AAC9B,MAAI,QAAQtB,QAAO;AACnB,MAAI,QAAQA,QAAO;AACnB,SAAO,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACnC,UAAM,OAAO,GAAG,GAAG,CAAC;AACpB,UAAM,OAAO,GAAG,GAAG,CAAC;AACpB,UAAM,KAAK,OAAO,OAAO,IAAI,KAAK,IAAI,EAAE;AACxC,WAAO;AAAA,EACT;AACF,EAAE;AAYK,IAAI,UAAU,WAAY;AAC/B,MAAI,OAAYA,QAAO;AACvB,SAAO,SAAU,KAAK,MAAM,OAAO,IAAI;AACrC,SAAK,CAAC,IAAI,MAAM,CAAC;AACjB,SAAK,CAAC,IAAI,MAAM,CAAC;AACjB,SAAK,CAAC,IAAI,MAAM,CAAC;AACjB,SAAK,CAAC,IAAI,GAAG,CAAC;AACd,SAAK,CAAC,IAAI,GAAG,CAAC;AACd,SAAK,CAAC,IAAI,GAAG,CAAC;AACd,SAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,SAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,SAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACjB,WAAOsB,WAAU,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,EAC3C;AACF,EAAE;;;AGrsBF;AAAA;AAAA,aAAAG;AAAA,EAAA,aAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,+BAAAC;AAAA,EAAA;AAAA,yBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA;AAAA;AAAA,wBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,iBAAAC;AAAA;AAiBO,SAASC,UAAS;AACvB,MAAI,KAAK,IAAa,WAAW,CAAC;AAElC,MAAa,cAAc,cAAc;AACvC,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AAAA,EACV;AAEA,KAAG,CAAC,IAAI;AACR,SAAO;AACT;AASO,SAASC,OAAM,GAAG;AACvB,MAAI,KAAK,IAAa,WAAW,CAAC;AAClC,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,KAAG,CAAC,IAAI,EAAE,CAAC;AACX,SAAO;AACT;AAgBO,SAASC,YAAW,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACzD,MAAI,KAAK,IAAa,WAAW,CAAC;AAClC,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,SAAO;AACT;AAeO,SAAS,8BAA8B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxE,MAAI,KAAK,IAAa,WAAW,CAAC;AAClC,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,KAAG,CAAC,IAAI;AACR,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK;AACd,KAAG,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AACjC,KAAG,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AACjC,KAAG,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AACjC,KAAG,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,SAAO;AACT;AAWO,SAASC,yBAAwB,KAAK,GAAG,GAAG;AACjD,MAAI,KAAK,EAAE,CAAC,IAAI,KACZ,KAAK,EAAE,CAAC,IAAI,KACZ,KAAK,EAAE,CAAC,IAAI,KACZ,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK;AACnC,SAAO;AACT;AAUO,SAASC,iBAAgB,KAAK,GAAG;AACtC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,cAAa,KAAK,GAAG;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG;AAE/B,MAAI,QAAaN,QAAO;AACxB,EAAK,YAAY,OAAO,CAAC;AACzB,MAAI,IAAI,IAAa,WAAW,CAAC;AACjC,EAAK,eAAe,GAAG,CAAC;AACxB,EAAAG,yBAAwB,KAAK,OAAO,CAAC;AACrC,SAAO;AACT;AAUO,SAASI,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAASC,UAAS,KAAK;AAC5B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAiBO,SAASC,KAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACvD,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,IAAI,UAAeF;AAQnB,SAAS,QAAQ,KAAK,GAAG;AAC9B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAUO,IAAI,UAAeA;AAUnB,SAAS,QAAQ,KAAK,GAAG;AAC9B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAQO,SAASG,gBAAe,KAAK,GAAG;AACrC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACnD,MAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACnD,MAAI,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACnD,SAAO;AACT;AAUO,SAASC,WAAU,KAAK,GAAG,GAAG;AACnC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,IAAI,KACb,MAAM,EAAE,CAAC,IAAI,KACb,MAAM,EAAE,CAAC,IAAI,KACb,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC7C,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC7C,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC7C,MAAI,CAAC,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAC9C,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC7C,EAAKA,SAAQ,KAAK,GAAG,GAAG;AACxB,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC7C,EAAKA,SAAQ,KAAK,GAAG,GAAG;AACxB,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,SAAO;AACT;AAUO,SAASC,SAAQ,KAAK,GAAG,KAAK;AACnC,MAAI,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,CAAC,EAAE,CAAC,GACT,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACzC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC7C,EAAKA,SAAQ,KAAK,GAAG,GAAG;AACxB,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,SAAO;AACT;AAUO,SAAS,mBAAmB,KAAK,GAAG,GAAG;AAC5C,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,SAAO;AACT;AAUO,SAAS,oBAAoB,KAAK,GAAG,GAAG;AAC7C,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,OAAK,EAAE,CAAC;AACR,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,SAAO;AACT;AAWO,SAAS,iBAAiB,KAAK,GAAG,MAAM,KAAK;AAElD,MAAI,KAAK,IAAI,GAAG,IAAa,SAAS;AACpC,WAAOP,MAAK,KAAK,CAAC;AAAA,EACpB;AAEA,MAAI,aAAa,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACrD,QAAM,MAAM;AACZ,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,KAAK,IAAI,KAAK,CAAC,IAAI;AACvB,MAAI,KAAK,IAAI,KAAK,CAAC,IAAI;AACvB,MAAI,KAAK,IAAI,KAAK,CAAC,IAAI;AACvB,MAAI,KAAK,KAAK,IAAI,GAAG;AACrB,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAChD,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,MAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C,SAAO;AACT;AAWO,SAASQ,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC,GACT,MAAM,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnD,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnD,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnD,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnD,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnG,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnG,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnG,MAAI,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACnG,SAAO;AACT;AAMO,IAAIC,OAAMD;AAWV,SAASE,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAUO,IAAIC,OAAWA;AAYf,SAASC,MAAK,KAAK,GAAG,GAAG,GAAG;AACjC,MAAI,KAAK,IAAI;AACb,MAAID,KAAI,GAAG,CAAC,IAAI,EAAG,KAAI,CAAC;AACxB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI;AAC5B,SAAO;AACT;AASO,SAASE,QAAO,KAAK,GAAG;AAC7B,MAAI,QAAQC,eAAc,CAAC;AAC3B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAUO,SAASC,WAAU,KAAK,GAAG;AAChC,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AASO,IAAIC,UAAcA;AAMlB,IAAIC,OAAMD;AASV,IAAIF,iBAAqBA;AAMzB,IAAII,UAASJ;AAUb,SAASK,WAAU,KAAK,GAAG;AAChC,MAAI,YAAYL,eAAc,CAAC;AAE/B,MAAI,YAAY,GAAG;AACjB,gBAAY,KAAK,KAAK,SAAS;AAC/B,QAAI,KAAK,EAAE,CAAC,IAAI;AAChB,QAAI,KAAK,EAAE,CAAC,IAAI;AAChB,QAAI,KAAK,EAAE,CAAC,IAAI;AAChB,QAAI,KAAK,EAAE,CAAC,IAAI;AAChB,QAAI,KAAK,EAAE,CAAC;AACZ,QAAI,KAAK,EAAE,CAAC;AACZ,QAAI,KAAK,EAAE,CAAC;AACZ,QAAI,KAAK,EAAE,CAAC;AACZ,QAAI,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACjD,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,KAAK,KAAK,KAAK,WAAW;AAC/B,QAAI,CAAC,KAAK,KAAK,KAAK,WAAW;AAC/B,QAAI,CAAC,KAAK,KAAK,KAAK,WAAW;AAC/B,QAAI,CAAC,KAAK,KAAK,KAAK,WAAW;AAAA,EACjC;AAEA,SAAO;AACT;AAQO,SAASM,KAAI,GAAG;AACrB,SAAO,WAAW,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AAC7H;AASO,SAASC,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAC5I;AASO,SAASC,QAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAC5qB;;;ACl0BA;AAAA;AAAA,aAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA,uBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,YAAAC;AAAA;AAYO,SAASC,UAAS;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AAEnC,MAAa,cAAc,cAAc;AACvC,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACX;AAEA,SAAO;AACT;AAQO,SAASC,OAAM,GAAG;AACvB,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AASO,SAASC,YAAW,GAAG,GAAG;AAC/B,MAAI,MAAM,IAAa,WAAW,CAAC;AACnC,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,UAAS,KAAK,GAAG,GAAG;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AAUO,SAASC,QAAO,KAAK,GAAG,GAAG;AAChC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,SAAO;AACT;AASO,SAASC,MAAK,KAAK,GAAG;AAC3B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AACvB,SAAO;AACT;AASO,SAASC,OAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AAUO,SAASC,KAAI,KAAK,GAAG,GAAG;AAC7B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,SAAO;AACT;AASO,SAASC,OAAM,KAAK,GAAG;AAC5B,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,MAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACxB,SAAO;AACT;AAUO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAChB,SAAO;AACT;AAWO,SAASC,aAAY,KAAK,GAAG,GAAGD,SAAO;AAC5C,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAIA;AACvB,SAAO;AACT;AASO,SAASE,UAAS,GAAG,GAAG;AAC7B,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GACd,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,KAAK,MAAM,GAAG,CAAC;AACxB;AASO,SAASC,iBAAgB,GAAG,GAAG;AACpC,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GACd,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO,IAAI,IAAI,IAAI;AACrB;AAQO,SAASC,QAAO,GAAG;AACxB,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,SAAO,KAAK,MAAM,GAAG,CAAC;AACxB;AAQO,SAASC,eAAc,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,SAAO,IAAI,IAAI,IAAI;AACrB;AASO,SAASC,QAAO,KAAK,GAAG;AAC7B,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,MAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACb,SAAO;AACT;AASO,SAASC,SAAQ,KAAK,GAAG;AAC9B,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,MAAI,CAAC,IAAI,IAAM,EAAE,CAAC;AAClB,SAAO;AACT;AASO,SAASC,WAAU,KAAK,GAAG;AAChC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAIC,OAAM,IAAI,IAAI,IAAI;AAEtB,MAAIA,OAAM,GAAG;AAEX,IAAAA,OAAM,IAAI,KAAK,KAAKA,IAAG;AAAA,EACzB;AAEA,MAAI,CAAC,IAAI,EAAE,CAAC,IAAIA;AAChB,MAAI,CAAC,IAAI,EAAE,CAAC,IAAIA;AAChB,SAAO;AACT;AASO,SAASC,KAAI,GAAG,GAAG;AACxB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AAWO,SAASC,OAAM,KAAK,GAAG,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChC,MAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AAClB,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAWO,SAASC,MAAK,KAAK,GAAG,GAAG,GAAG;AACjC,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,MAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,IAAI;AAC1B,SAAO;AACT;AASO,SAASC,QAAO,KAAKb,SAAO;AACjC,EAAAA,UAAQA,WAAS;AACjB,MAAI,IAAa,OAAO,IAAI,IAAM,KAAK;AACvC,MAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAIA;AACvB,MAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAIA;AACvB,SAAO;AACT;AAUO,SAAS,cAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAC3B,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AAC3B,SAAO;AACT;AAUO,SAAS,eAAe,KAAK,GAAG,GAAG;AACxC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAO;AACT;AAWO,SAASc,eAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC,GACP,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAClC,SAAO;AACT;AAYO,SAASC,eAAc,KAAK,GAAG,GAAG;AACvC,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,IAAI,EAAE,CAAC;AACX,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AACnC,MAAI,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AACnC,SAAO;AACT;AAUO,SAASC,QAAO,KAAK,GAAG,GAAG,KAAK;AAErC,MAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GACf,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GACf,OAAO,KAAK,IAAI,GAAG,GACnB,OAAO,KAAK,IAAI,GAAG;AAEvB,MAAI,CAAC,IAAI,KAAK,OAAO,KAAK,OAAO,EAAE,CAAC;AACpC,MAAI,CAAC,IAAI,KAAK,OAAO,KAAK,OAAO,EAAE,CAAC;AACpC,SAAO;AACT;AAQO,SAASC,OAAM,GAAG,GAAG;AAC1B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GAEZ,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAEhE,SAAS,QAAQ,KAAK,KAAK,KAAK,MAAM;AAEtC,SAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,QAAQ,EAAE,GAAG,CAAC,CAAC;AACpD;AAQO,SAASC,MAAK,KAAK;AACxB,MAAI,CAAC,IAAI;AACT,MAAI,CAAC,IAAI;AACT,SAAO;AACT;AAQO,SAASC,KAAI,GAAG;AACrB,SAAO,UAAU,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACxC;AASO,SAASC,aAAY,GAAG,GAAG;AAChC,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AACtC;AASO,SAASC,SAAO,GAAG,GAAG;AAC3B,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC;AACZ,SAAO,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,KAAK,EAAE,KAAc,UAAU,KAAK,IAAI,GAAK,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAC9K;AAMO,IAAIZ,OAAML;AAMV,IAAIkB,OAAM9B;AAMV,IAAI+B,OAAM9B;AAMV,IAAI+B,OAAM9B;AAMV,IAAI+B,QAAOvB;AAMX,IAAIwB,WAAUvB;AAMd,IAAIwB,UAAStB;AAcb,IAAIuB,WAAU,WAAY;AAC/B,MAAI,MAAM1C,QAAO;AACjB,SAAO,SAAU,GAAG,QAAQ,QAAQ,OAAO,IAAI,KAAK;AAClD,QAAI,GAAG;AAEP,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,IACX;AAEA,QAAI,OAAO;AACT,UAAI,KAAK,IAAI,QAAQ,SAAS,QAAQ,EAAE,MAAM;AAAA,IAChD,OAAO;AACL,UAAI,EAAE;AAAA,IACR;AAEA,SAAK,IAAI,QAAQ,IAAI,GAAG,KAAK,QAAQ;AACnC,UAAI,CAAC,IAAI,EAAE,CAAC;AACZ,UAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAChB,SAAG,KAAK,KAAK,GAAG;AAChB,QAAE,CAAC,IAAI,IAAI,CAAC;AACZ,QAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAClB;AAEA,WAAO;AAAA,EACT;AACF,EAAE;", "names": ["equals", "equals", "scale", "add", "clone", "copy", "create", "determinant", "equals", "exactEquals", "frob", "fromRotation", "fromScaling", "fromValues", "identity", "invert", "mul", "multiply", "multiplyScalar", "multiplyScalarAndAdd", "rotate", "scale", "set", "str", "sub", "subtract", "create", "clone", "copy", "identity", "fromValues", "set", "invert", "determinant", "multiply", "rotate", "scale", "fromRotation", "fromScaling", "str", "frob", "add", "subtract", "multiplyScalar", "multiplyScalarAndAdd", "exactEquals", "equals", "mul", "sub", "add", "adjoint", "clone", "copy", "create", "determinant", "equals", "exactEquals", "frob", "fromRotation", "fromScaling", "fromTranslation", "fromValues", "identity", "invert", "mul", "multiply", "multiplyScalar", "multiplyScalarAndAdd", "rotate", "scale", "set", "str", "sub", "subtract", "translate", "transpose", "create", "clone", "copy", "fromValues", "set", "identity", "transpose", "invert", "adjoint", "determinant", "multiply", "translate", "rotate", "scale", "fromTranslation", "fromRotation", "fromScaling", "str", "frob", "add", "subtract", "multiplyScalar", "multiplyScalarAndAdd", "exactEquals", "equals", "mul", "sub", "add", "adjoint", "clone", "copy", "create", "determinant", "equals", "exactEquals", "frob", "fromQuat", "fromRotation", "fromScaling", "fromTranslation", "fromValues", "identity", "invert", "mul", "multiply", "multiplyScalar", "multiplyScalarAndAdd", "rotate", "scale", "set", "str", "sub", "subtract", "translate", "transpose", "create", "clone", "copy", "fromValues", "set", "identity", "transpose", "invert", "adjoint", "determinant", "multiply", "translate", "scale", "rotate", "len", "fromTranslation", "fromScaling", "fromRotation", "fromQuat", "str", "frob", "add", "subtract", "multiplyScalar", "multiplyScalarAndAdd", "exactEquals", "equals", "mul", "sub", "add", "clone", "copy", "create", "dot", "equals", "exactEquals", "fromValues", "identity", "invert", "len", "length", "lerp", "mul", "multiply", "normalize", "random", "rotateX", "rotateY", "rotateZ", "scale", "set", "sqrLen", "squared<PERSON>ength", "str", "add", "clone", "copy", "create", "equals", "exactEquals", "fromValues", "mul", "multiply", "rotateX", "rotateY", "rotateZ", "scale", "set", "str", "sub", "subtract", "create", "clone", "fromValues", "copy", "set", "add", "subtract", "multiply", "scale", "len", "rotateX", "rotateY", "rotateZ", "str", "exactEquals", "equals", "sub", "mul", "add", "ceil", "clone", "copy", "create", "cross", "dist", "distance", "div", "divide", "dot", "equals", "exactEquals", "floor", "for<PERSON>ach", "fromValues", "inverse", "len", "length", "lerp", "max", "min", "mul", "multiply", "negate", "normalize", "random", "round", "scale", "scaleAndAdd", "set", "sqrDist", "sqrLen", "squaredDistance", "squared<PERSON>ength", "str", "sub", "subtract", "transformMat4", "transformQuat", "zero", "create", "clone", "fromValues", "copy", "set", "add", "subtract", "multiply", "divide", "ceil", "floor", "min", "max", "round", "scale", "scaleAndAdd", "distance", "squaredDistance", "length", "squared<PERSON>ength", "negate", "inverse", "normalize", "len", "dot", "cross", "lerp", "random", "transformMat4", "transformQuat", "zero", "str", "exactEquals", "equals", "sub", "mul", "div", "dist", "sqrDist", "sqrLen", "for<PERSON>ach", "create", "identity", "dot", "multiply", "rotateX", "rotateY", "rotateZ", "scale", "random", "invert", "str", "clone", "fromValues", "copy", "set", "add", "mul", "lerp", "length", "len", "squared<PERSON>ength", "sqrLen", "normalize", "exactEquals", "equals", "add", "clone", "conjugate", "copy", "create", "dot", "equals", "exactEquals", "fromMat4", "fromRotation", "fromRotationTranslation", "fromTranslation", "fromValues", "getTranslation", "identity", "invert", "len", "length", "lerp", "mul", "multiply", "normalize", "rotateX", "rotateY", "rotateZ", "scale", "set", "sqrLen", "squared<PERSON>ength", "str", "translate", "create", "clone", "fromValues", "fromRotationTranslation", "fromTranslation", "fromRotation", "fromMat4", "copy", "identity", "set", "getTranslation", "translate", "rotateX", "rotateY", "rotateZ", "add", "multiply", "mul", "scale", "dot", "lerp", "invert", "squared<PERSON>ength", "conjugate", "length", "len", "sqrLen", "normalize", "str", "exactEquals", "equals", "add", "angle", "ceil", "clone", "copy", "create", "cross", "dist", "distance", "div", "divide", "dot", "equals", "exactEquals", "floor", "for<PERSON>ach", "fromValues", "inverse", "len", "length", "lerp", "max", "min", "mul", "multiply", "negate", "normalize", "random", "rotate", "round", "scale", "scaleAndAdd", "set", "sqrDist", "sqrLen", "squaredDistance", "squared<PERSON>ength", "str", "sub", "subtract", "transformMat3", "transformMat4", "zero", "create", "clone", "fromValues", "copy", "set", "add", "subtract", "multiply", "divide", "ceil", "floor", "min", "max", "round", "scale", "scaleAndAdd", "distance", "squaredDistance", "length", "squared<PERSON>ength", "negate", "inverse", "normalize", "len", "dot", "cross", "lerp", "random", "transformMat3", "transformMat4", "rotate", "angle", "zero", "str", "exactEquals", "equals", "sub", "mul", "div", "dist", "sqrDist", "sqrLen", "for<PERSON>ach"]}