import React, { forwardRef } from 'react';
import { useCachedImage } from '../hooks/useCachedImage';

interface CachedImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  src: string;
  fallback?: string;
  loadIfNotCached?: boolean;
  onLoadingChange?: (isLoading: boolean) => void;
  onError?: (error: string) => void;
  placeholder?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

/**
 * CachedImage component that uses preloaded/cached images
 * This prevents images from reloading when components mount
 */
export const CachedImage = forwardRef<HTMLImageElement, CachedImageProps>(({
  src,
  fallback,
  loadIfNotCached = true,
  onLoadingChange,
  onError,
  placeholder,
  errorComponent,
  className = '',
  alt = '',
  ...props
}, ref) => {
  const { 
    src: cachedSrc, 
    isLoaded, 
    isLoading, 
    error 
  } = useCachedImage(src, { fallback, loadIfNotCached });

  // Notify parent of loading state changes
  React.useEffect(() => {
    onLoadingChange?.(isLoading);
  }, [isLoading, onLoadingChange]);

  // Notify parent of errors
  React.useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  // Show placeholder while loading
  if (isLoading && placeholder) {
    return <>{placeholder}</>;
  }

  // Show error component if failed and no fallback worked
  if (error && !isLoaded && errorComponent) {
    return <>{errorComponent}</>;
  }

  // Render the image
  return (
    <img
      ref={ref}
      src={cachedSrc}
      alt={alt}
      className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
      {...props}
      onLoad={(e) => {
        // Call original onLoad if provided
        props.onLoad?.(e);
      }}
      onError={(e) => {
        // Call original onError if provided
        props.onError?.(e);
      }}
    />
  );
});

CachedImage.displayName = 'CachedImage';

/**
 * Background image component using cached images
 */
interface CachedBackgroundImageProps {
  src: string;
  fallback?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  loadIfNotCached?: boolean;
}

export const CachedBackgroundImage: React.FC<CachedBackgroundImageProps> = ({
  src,
  fallback,
  children,
  className = '',
  style = {},
  loadIfNotCached = true
}) => {
  const { src: cachedSrc, isLoaded } = useCachedImage(src, { fallback, loadIfNotCached });

  return (
    <div
      className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
      style={{
        ...style,
        backgroundImage: `url(${cachedSrc})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {children}
    </div>
  );
};

/**
 * Image with loading skeleton
 */
interface CachedImageWithSkeletonProps extends CachedImageProps {
  skeletonClassName?: string;
  aspectRatio?: string; // e.g., "16/9", "1/1", "4/3"
}

export const CachedImageWithSkeleton: React.FC<CachedImageWithSkeletonProps> = ({
  skeletonClassName = '',
  aspectRatio = '16/9',
  className = '',
  ...props
}) => {
  const { isLoaded, isLoading } = useCachedImage(props.src, {
    fallback: props.fallback,
    loadIfNotCached: props.loadIfNotCached
  });

  return (
    <div className={`relative overflow-hidden ${className}`} style={{ aspectRatio }}>
      {/* Skeleton loader */}
      {(isLoading || !isLoaded) && (
        <div 
          className={`absolute inset-0 bg-gray-200 animate-pulse ${skeletonClassName}`}
          style={{ aspectRatio }}
        />
      )}
      
      {/* Actual image */}
      <CachedImage
        {...props}
        className={`absolute inset-0 w-full h-full object-cover ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
      />
    </div>
  );
};

export default CachedImage;
