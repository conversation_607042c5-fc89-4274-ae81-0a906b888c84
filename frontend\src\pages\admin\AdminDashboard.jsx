import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  Users,
  FileText,
  Briefcase,
  GraduationCap
} from 'lucide-react';
import {
  quoteRequestAPI,
  jobApplicationAPI,
  internshipApplicationAPI
} from '../../lib/api';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalEmployees: 0,
    totalInterns: 0,
    jobApplications: 0,
    internshipApplications: 0,
    quoteRequests: 0,
    pendingReviews: 0
  });

  const [quoteRequests, setQuoteRequests] = useState([]);
  const [jobApplications, setJobApplications] = useState([]);
  const [internshipApplications, setInternshipApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [quotesRes, jobsRes, internsRes] = await Promise.all([
          quoteRequestAPI.getAll(),
          jobApplicationAPI.getAll(),
          internshipApplicationAPI.getAll()
        ]);

        setQuoteRequests(quotesRes?.data?.slice(0, 5) || []);
        setJobApplications(jobsRes?.data?.slice(0, 5) || []);
        setInternshipApplications(internsRes?.data?.slice(0, 5) || []);

        setStats({
          totalEmployees: 45,
          totalInterns: 12,
          quoteRequests: quotesRes?.data?.length || 0,
          jobApplications: jobsRes?.data?.length || 0,
          internshipApplications: internsRes?.data?.length || 0,
          pendingReviews:
            (jobsRes?.data?.filter(j => j.status === 'pending').length || 0) +
            (internsRes?.data?.filter(i => i.status === 'pending').length || 0)
        });
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message);

        setStats({
          totalEmployees: 45,
          totalInterns: 12,
          jobApplications: 28,
          internshipApplications: 15,
          quoteRequests: 12,
          pendingReviews: 8
        });

        setQuoteRequests([
          {
            id: 1,
            name: 'John Smith',
            email: '<EMAIL>',
            message: 'Need a website for my business',
            services: ['web-development'],
            status: 'new',
            created_at: new Date().toISOString()
          }
        ]);
        setJobApplications([
          {
            id: 1,
            first_name: 'Jane',
            last_name: 'Doe',
            position: 'Frontend Developer',
            status: 'pending',
            submitted_at: new Date().toISOString()
          }
        ]);
        setInternshipApplications([
          {
            id: 1,
            first_name: 'Mike',
            last_name: 'Johnson',
            position: 'UI/UX Design Intern',
            status: 'pending',
            submitted_at: new Date().toISOString()
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-white">Hello, {user?.first_name || user?.username || 'Admin'}</h1>
          <p className="text-gray-400">Welcome to your admin dashboard</p>
        </div>
        <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-xl">
          <Users size={16} className="inline-block mr-2" /> Manage Users
        </button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {[
          {
            title: 'Quote Requests',
            value: stats.quoteRequests,
            color: 'text-blue-400'
          },
          {
            title: 'Job Applications',
            value: stats.jobApplications,
            color: 'text-purple-400'
          },
          {
            title: 'Internship Applications',
            value: stats.internshipApplications,
            color: 'text-green-400'
          }
        ].map((card, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.1 }}
            className={`bg-dark-800 border border-gray-700 rounded-xl p-6 text-center ${card.color}`}
          >
            <h2 className="text-lg font-semibold mb-1">{card.title}</h2>
            <p className="text-4xl font-bold">{card.value}</p>
          </motion.div>
        ))}
      </div>

      {/* Placeholder or rendering of quotes, job, and internship applications would continue below... */}
    </div>
  );
};

export default AdminDashboard;
