import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import {
  Users,
  FileText,
  Briefcase,
  GraduationCap
} from 'lucide-react'
import { quoteRequestAPI, jobApplicationAPI, internshipApplicationAPI } from '../../lib/api'

const AdminDashboard = () => {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    totalEmployees: 0,
    totalInterns: 0,
    jobApplications: 0,
    internshipApplications: 0,
    quoteRequests: 0,
    pendingReviews: 0
  })

  const [quoteRequests, setQuoteRequests] = useState([])
  const [jobApplications, setJobApplications] = useState([])
  const [internshipApplications, setInternshipApplications] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch dashboard data on component mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)

        // Fetch all data in parallel for better performance
        const [
          { data: quotesData, error: quotesError },
          { data: jobsData, error: jobsError },
          { data: internsData, error: internsError }
        ] = await Promise.all([
          quoteRequestAPI.getAll(),
          jobApplicationAPI.getAll(),
          internshipApplicationAPI.getAll()
        ])

        // Handle errors gracefully
        if (quotesError) console.error('Error fetching quotes:', quotesError)
        if (jobsError) console.error('Error fetching job applications:', jobsError)
        if (internsError) console.error('Error fetching internship applications:', internsError)

        // Set the data for each section (show latest 5)
        setQuoteRequests(quotesData?.slice(0, 5) || [])
        setJobApplications(jobsData?.slice(0, 5) || [])
        setInternshipApplications(internsData?.slice(0, 5) || [])

        // Update stats with real counts
        setStats({
          totalEmployees: 45, // This would come from employee management system
          totalInterns: 12,   // This would come from employee management system
          quoteRequests: quotesData?.length || 0,
          jobApplications: jobsData?.length || 0,
          internshipApplications: internsData?.length || 0,
          pendingReviews: (jobsData?.filter(j => j.status === 'pending').length || 0) +
                         (internsData?.filter(i => i.status === 'pending').length || 0)
        })

        setError(null)

      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError(err.message)

        // Fallback to mock data if API fails
        setStats({
          totalEmployees: 45,
          totalInterns: 12,
          jobApplications: 28,
          internshipApplications: 15,
          quoteRequests: 12,
          pendingReviews: 8
        })

        // Set mock data for the sections
        setQuoteRequests([
          { id: 1, name: 'John Smith', email: '<EMAIL>', message: 'Need a website for my business', services: ['web-development'], status: 'new', created_at: new Date().toISOString() }
        ])
        setJobApplications([
          { id: 1, first_name: 'Jane', last_name: 'Doe', position: 'Frontend Developer', status: 'pending', submitted_at: new Date().toISOString() }
        ])
        setInternshipApplications([
          { id: 1, first_name: 'Mike', last_name: 'Johnson', position: 'UI/UX Design Intern', status: 'pending', submitted_at: new Date().toISOString() }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Helper function to format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Less than an hour ago'
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`

    const diffInWeeks = Math.floor(diffInDays / 7)
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`
  }

  const statsCards = [
    {
      title: 'Quote Requests',
      value: stats.quoteRequests,
      subtitle: 'Total requests',
      color: 'bg-blue-900/50 border-blue-500/30',
      textColor: 'text-blue-400'
    },
    {
      title: 'Job Applications',
      value: stats.jobApplications,
      subtitle: 'Total candidates',
      color: 'bg-purple-900/50 border-purple-500/30',
      textColor: 'text-purple-400'
    },
    {
      title: 'Internship Applications',
      value: stats.internshipApplications,
      subtitle: 'Total interns',
      color: 'bg-green-900/50 border-green-500/30',
      textColor: 'text-green-400'
    }
  ]

  const quickActions = [
    { title: 'Review Applications', icon: FileText, href: '/admin/applications', color: 'bg-purple-500' },
    { title: 'Manage Quotes', icon: Briefcase, href: '/admin/quotes', color: 'bg-blue-500' }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-6 md:mb-8">
          <div className="mb-4 md:mb-0">
            <h1 className="text-2xl md:text-4xl font-bold text-white mb-2">
              Hello, {user?.first_name || user?.username || 'Admin'}
            </h1>
            <p className="text-gray-400 text-sm md:text-lg">
              Welcome to your admin dashboard {user?.role && <span className="capitalize">• {user.role}</span>}
            </p>
          </div>
          <div className="flex items-center space-x-2 md:space-x-4">
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 md:px-6 py-2 md:py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 text-sm md:text-base">
              <Users size={16} />
              <span className="hidden sm:inline">Manage Users</span>
              <span className="sm:hidden">Users</span>
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8">
          {statsCards.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`${stat.color} border rounded-xl md:rounded-2xl p-4 md:p-8 text-center hover:scale-105 transition-all duration-300`}
            >
              <h3 className={`text-sm md:text-lg font-semibold ${stat.textColor} mb-2`}>{stat.title}</h3>
              <div className={`text-3xl md:text-5xl font-bold ${stat.textColor} mb-2`}>{stat.value}</div>
              <p className="text-gray-400 text-xs md:text-sm">{stat.subtitle}</p>
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-8">
          {/* Recent Quote Requests */}
          <div className="lg:col-span-1">
            <div className="bg-dark-800/50 border border-gray-700/50 rounded-xl md:rounded-2xl p-4 md:p-6">
              <div className="flex justify-between items-center mb-4 md:mb-6">
                <h2 className="text-lg md:text-xl font-bold text-white">Recent Quote Requests</h2>
                <a href="/admin/quotes" className="text-blue-400 hover:text-blue-300 text-xs md:text-sm font-medium">View all →</a>
              </div>
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading...</p>
                </div>
              ) : quoteRequests.length > 0 ? (
                <div className="space-y-4">
                  {quoteRequests.map((quote) => (
                    <motion.div
                      key={quote.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-dark-700/30 border border-gray-600/30 rounded-xl p-4 hover:bg-dark-700/50 transition-all duration-300"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-white font-medium text-sm">{quote.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          quote.status === 'new' ? 'bg-green-500/20 text-green-400' :
                          quote.status === 'reviewing' ? 'bg-blue-500/20 text-blue-400' :
                          quote.status === 'quoted' ? 'bg-purple-500/20 text-purple-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {quote.status}
                        </span>
                      </div>
                      <p className="text-gray-400 text-xs mb-2">{quote.email}</p>
                      <p className="text-gray-300 text-sm mb-3 line-clamp-2">{quote.message}</p>
                      <div className="flex justify-between items-center">
                        <div className="flex flex-wrap gap-1">
                          {quote.services?.slice(0, 2).map((service, index) => (
                            <span key={index} className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs">
                              {service}
                            </span>
                          ))}
                          {quote.services?.length > 2 && (
                            <span className="text-gray-400 text-xs">+{quote.services.length - 2} more</span>
                          )}
                        </div>
                        <span className="text-gray-500 text-xs">{formatTimeAgo(quote.created_at)}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="text-gray-600 mx-auto mb-4" size={48} />
                  <p className="text-gray-400">No quote requests found</p>
                </div>
              )}
            </div>
          </div>

          {/* Job Applications */}
          <div className="lg:col-span-1">
            <div className="bg-dark-800/50 border border-gray-700/50 rounded-xl md:rounded-2xl p-4 md:p-6">
              <div className="flex justify-between items-center mb-4 md:mb-6">
                <h2 className="text-lg md:text-xl font-bold text-white">Recent Job Applications</h2>
                <a href="/admin/applications" className="text-purple-400 hover:text-purple-300 text-xs md:text-sm font-medium">View all →</a>
              </div>
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading...</p>
                </div>
              ) : jobApplications.length > 0 ? (
                <div className="space-y-4">
                  {jobApplications.map((application, index) => (
                    <motion.div
                      key={application.id}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="bg-dark-700/30 border border-gray-600/30 rounded-xl p-4 hover:bg-dark-700/50 transition-all duration-300"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-white font-medium text-sm">{application.first_name} {application.last_name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          application.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                          application.status === 'reviewing' ? 'bg-blue-500/20 text-blue-400' :
                          application.status === 'approved' ? 'bg-green-500/20 text-green-400' :
                          'bg-red-500/20 text-red-400'
                        }`}>
                          {application.status}
                        </span>
                      </div>
                      <p className="text-gray-400 text-xs mb-2">{application.position}</p>
                      <p className="text-gray-500 text-xs">{formatTimeAgo(application.submitted_at)}</p>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Briefcase className="text-gray-600 mx-auto mb-4" size={48} />
                  <p className="text-gray-400">No job applications found</p>
                </div>
              )}
            </div>
          </div>

          {/* Internship Applications */}
          <div className="lg:col-span-1">
            <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-white">Recent Internship Applications</h2>
                <a href="/admin/applications" className="text-green-400 hover:text-green-300 text-sm font-medium">View all →</a>
              </div>
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading...</p>
                </div>
              ) : internshipApplications.length > 0 ? (
                <div className="space-y-4">
                  {internshipApplications.map((application, index) => (
                    <motion.div
                      key={application.id}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="bg-dark-700/30 border border-gray-600/30 rounded-xl p-4 hover:bg-dark-700/50 transition-all duration-300"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-white font-medium text-sm">{application.first_name} {application.last_name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          application.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                          application.status === 'reviewing' ? 'bg-blue-500/20 text-blue-400' :
                          application.status === 'approved' ? 'bg-green-500/20 text-green-400' :
                          'bg-red-500/20 text-red-400'
                        }`}>
                          {application.status}
                        </span>
                      </div>
                      <p className="text-gray-400 text-xs mb-2">{application.position}</p>
                      <p className="text-gray-500 text-xs">{formatTimeAgo(application.submitted_at)}</p>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <GraduationCap className="text-gray-600 mx-auto mb-4" size={48} />
                  <p className="text-gray-400">No internship applications found</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Performance Chart Placeholder */}

      </div>
    
  )
}

export default AdminDashboard
