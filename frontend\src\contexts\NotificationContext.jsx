import React, { createContext, useContext, useState, useEffect } from 'react';
import { quoteRequestAPI, jobApplicationAPI, internshipApplicationAPI } from '../lib/api';

const NotificationContext = createContext();

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    // Return default values when context is not available
    return {
      notifications: [],
      loading: false,
      unreadCount: 0,
      markAsRead: () => {},
      markAllAsRead: () => {},
      addNotification: () => {},
      removeNotification: () => {},
      refreshNotifications: () => {}
    };
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch initial notifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      
      // Fetch recent applications and quotes
      const [quotesResponse, jobsResponse, internsResponse] = await Promise.all([
        quoteRequestAPI.getAll(),
        jobApplicationAPI.getAll(),
        internshipApplicationAPI.getAll()
      ]);

      const notifications = [];

      // Add quote notifications (last 5)
      if (quotesResponse.data) {
        quotesResponse.data
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 5)
          .forEach(quote => {
            notifications.push({
              id: `quote-${quote.id}`,
              type: 'quote',
              title: 'New Quote Request',
              message: `Quote request from ${quote.name}`,
              time: formatTimeAgo(quote.created_at),
              unread: isRecent(quote.created_at),
              data: quote,
              link: '/admin/quotes'
            });
          });
      }

      // Add job application notifications (last 5)
      if (jobsResponse.data) {
        jobsResponse.data
          .sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at))
          .slice(0, 5)
          .forEach(application => {
            notifications.push({
              id: `job-${application.id}`,
              type: 'job_application',
              title: 'New Job Application',
              message: `Application from ${application.first_name} ${application.last_name} for ${application.position}`,
              time: formatTimeAgo(application.submitted_at),
              unread: isRecent(application.submitted_at),
              data: application,
              link: '/admin/applications'
            });
          });
      }

      // Add internship application notifications (last 5)
      if (internsResponse.data) {
        internsResponse.data
          .sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at))
          .slice(0, 5)
          .forEach(application => {
            notifications.push({
              id: `intern-${application.id}`,
              type: 'internship_application',
              title: 'New Internship Application',
              message: `Internship application from ${application.first_name} ${application.last_name} for ${application.position}`,
              time: formatTimeAgo(application.submitted_at),
              unread: isRecent(application.submitted_at),
              data: application,
              link: '/admin/applications'
            });
          });
      }

      // Sort all notifications by time (most recent first)
      notifications.sort((a, b) => {
        const timeA = a.data.created_at || a.data.submitted_at;
        const timeB = b.data.created_at || b.data.submitted_at;
        return new Date(timeB) - new Date(timeA);
      });

      setNotifications(notifications.slice(0, 10)); // Keep only 10 most recent
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Set some mock notifications if API fails
      setNotifications([
        {
          id: 'mock-1',
          type: 'quote',
          title: 'New Quote Request',
          message: 'Quote request from John Doe',
          time: '2 min ago',
          unread: true,
          link: '/admin/quotes'
        },
        {
          id: 'mock-2',
          type: 'job_application',
          title: 'New Job Application',
          message: 'Application from Jane Smith for Frontend Developer',
          time: '5 min ago',
          unread: true,
          link: '/admin/applications'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if a timestamp is recent (within last 24 hours)
  const isRecent = (timestamp) => {
    const now = new Date();
    const itemTime = new Date(timestamp);
    const diffHours = (now - itemTime) / (1000 * 60 * 60);
    return diffHours <= 24;
  };

  // Helper function to format time ago
  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const itemTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - itemTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes} min ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return itemTime.toLocaleDateString();
  };

  // Mark notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, unread: false }
          : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, unread: false }))
    );
  };

  // Add new notification (for real-time updates)
  const addNotification = (notification) => {
    const newNotification = {
      ...notification,
      id: notification.id || `new-${Date.now()}`,
      time: 'Just now',
      unread: true
    };
    
    setNotifications(prev => [newNotification, ...prev.slice(0, 9)]);
  };

  // Remove notification
  const removeNotification = (notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  // Get unread count
  const unreadCount = notifications.filter(n => n.unread).length;

  // Fetch notifications on mount and set up polling
  useEffect(() => {
    fetchNotifications();
    
    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const value = {
    notifications,
    loading,
    unreadCount,
    markAsRead,
    markAllAsRead,
    addNotification,
    removeNotification,
    refreshNotifications: fetchNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
