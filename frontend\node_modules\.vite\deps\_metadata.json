{"hash": "9a55e5a9", "configHash": "48a10469", "lockfileHash": "a514b3aa", "browserHash": "3859eb25", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f2fbe263", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "21d5c491", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4e8830b3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "612db535", "needsInterop": true}, "@emailjs/browser": {"src": "../../@emailjs/browser/es/index.js", "file": "@emailjs_browser.js", "fileHash": "a5c22db9", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "20ce4212", "needsInterop": false}, "bcryptjs": {"src": "../../bcryptjs/index.js", "file": "bcryptjs.js", "fileHash": "c4041214", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "7f0b1151", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "44d51420", "needsInterop": false}, "gl-matrix": {"src": "../../gl-matrix/esm/index.js", "file": "gl-matrix.js", "fileHash": "30feb15c", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "85219de6", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "249b7987", "needsInterop": false}, "lenis": {"src": "../../lenis/dist/lenis.mjs", "file": "lenis.js", "fileHash": "d2a896ea", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "9d6bdb23", "needsInterop": false}, "motion/react": {"src": "../../motion/dist/es/react.mjs", "file": "motion_react.js", "fileHash": "40387c8e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2a0d0e25", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "94b415fe", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "6a889f68", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "945e344e", "needsInterop": false}}, "chunks": {"browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "chunk-Y5BGZF4O": {"file": "chunk-Y5BGZF4O.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-ZY6GRSWM": {"file": "chunk-ZY6GRSWM.js"}, "chunk-VTIQK5XW": {"file": "chunk-VTIQK5XW.js"}, "chunk-H5FQS3OF": {"file": "chunk-H5FQS3OF.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}