{"version": 3, "sources": ["../../@emailjs/browser/es/models/EmailJSResponseStatus.js", "../../@emailjs/browser/es/utils/createWebStorage/createWebStorage.js", "../../@emailjs/browser/es/store/store.js", "../../@emailjs/browser/es/utils/buildOptions/buildOptions.js", "../../@emailjs/browser/es/methods/init/init.js", "../../@emailjs/browser/es/api/sendPost.js", "../../@emailjs/browser/es/utils/validateParams/validateParams.js", "../../@emailjs/browser/es/utils/validateTemplateParams/validateTemplateParams.js", "../../@emailjs/browser/es/utils/isHeadless/isHeadless.js", "../../@emailjs/browser/es/errors/headlessError/headlessError.js", "../../@emailjs/browser/es/utils/validateBlockListParams/validateBlockListParams.js", "../../@emailjs/browser/es/utils/isBlockedValueInParams/isBlockedValueInParams.js", "../../@emailjs/browser/es/errors/blockedEmailError/blockedEmailError.js", "../../@emailjs/browser/es/utils/validateLimitRateParams/validateLimitRateParams.js", "../../@emailjs/browser/es/utils/isLimitRateHit/isLimitRateHit.js", "../../@emailjs/browser/es/errors/limitRateError/limitRateError.js", "../../@emailjs/browser/es/methods/send/send.js", "../../@emailjs/browser/es/utils/validateForm/validateForm.js", "../../@emailjs/browser/es/methods/sendForm/sendForm.js", "../../@emailjs/browser/es/index.js"], "sourcesContent": ["export class EmailJSResponseStatus {\n    constructor(_status = 0, _text = 'Network Error') {\n        this.status = _status;\n        this.text = _text;\n    }\n}\n", "export const createWebStorage = () => {\n    if (typeof localStorage === 'undefined')\n        return;\n    return {\n        get: (key) => Promise.resolve(localStorage.getItem(key)),\n        set: (key, value) => Promise.resolve(localStorage.setItem(key, value)),\n        remove: (key) => Promise.resolve(localStorage.removeItem(key)),\n    };\n};\n", "import { createWebStorage } from '../utils/createWebStorage/createWebStorage';\nexport const store = {\n    origin: 'https://api.emailjs.com',\n    blockHeadless: false,\n    storageProvider: createWebStorage(),\n};\n", "export const buildOptions = (options) => {\n    if (!options)\n        return {};\n    // support compatibility with SDK v3\n    if (typeof options === 'string') {\n        return {\n            publicKey: options,\n        };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (options.toString() === '[object Object]') {\n        return options;\n    }\n    return {};\n};\n", "import { store } from '../../store/store';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\n/**\n * EmailJS global SDK config\n * @param {object} options - the EmailJS global SDK config options\n * @param {string} origin - the non-default EmailJS origin\n */\nexport const init = (options, origin = 'https://api.emailjs.com') => {\n    if (!options)\n        return;\n    const opts = buildOptions(options);\n    store.publicKey = opts.publicKey;\n    store.blockHeadless = opts.blockHeadless;\n    store.storageProvider = opts.storageProvider;\n    store.blockList = opts.blockList;\n    store.limitRate = opts.limitRate;\n    store.origin = opts.origin || origin;\n};\n", "import { EmailJSResponseStatus } from '../models/EmailJSResponseStatus';\nimport { store } from '../store/store';\nexport const sendPost = async (url, data, headers = {}) => {\n    const response = await fetch(store.origin + url, {\n        method: 'POST',\n        headers,\n        body: data,\n    });\n    const message = await response.text();\n    const responseStatus = new EmailJSResponseStatus(response.status, message);\n    if (response.ok) {\n        return responseStatus;\n    }\n    throw responseStatus;\n};\n", "export const validateParams = (publicKey, serviceID, templateID) => {\n    if (!publicKey || typeof publicKey !== 'string') {\n        throw 'The public key is required. Visit https://dashboard.emailjs.com/admin/account';\n    }\n    if (!serviceID || typeof serviceID !== 'string') {\n        throw 'The service ID is required. Visit https://dashboard.emailjs.com/admin';\n    }\n    if (!templateID || typeof templateID !== 'string') {\n        throw 'The template ID is required. Visit https://dashboard.emailjs.com/admin/templates';\n    }\n};\n", "export const validateTemplateParams = (templateParams) => {\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    if (templateParams && templateParams.toString() !== '[object Object]') {\n        throw 'The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/';\n    }\n};\n", "export const isHeadless = (navigator) => {\n    return navigator.webdriver || !navigator.languages || navigator.languages.length === 0;\n};\n", "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const headlessError = () => {\n    return new EmailJSResponseStatus(451, 'Unavailable For Headless Browser');\n};\n", "export const validateBlockListParams = (list, watchVariable) => {\n    if (!Array.isArray(list)) {\n        throw 'The BlockList list has to be an array';\n    }\n    if (typeof watchVariable !== 'string') {\n        throw 'The BlockList watchVariable has to be a string';\n    }\n};\n", "import { validateBlockListParams } from '../validateBlockListParams/validateBlockListParams';\nconst isBlockListDisabled = (options) => {\n    return !options.list?.length || !options.watchVariable;\n};\nconst getValue = (data, name) => {\n    return data instanceof FormData ? data.get(name) : data[name];\n};\nexport const isBlockedValueInParams = (options, params) => {\n    if (isBlockListDisabled(options))\n        return false;\n    validateBlockListParams(options.list, options.watchVariable);\n    const value = getValue(params, options.watchVariable);\n    if (typeof value !== 'string')\n        return false;\n    return options.list.includes(value);\n};\n", "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const blockedEmailError = () => {\n    return new EmailJSResponseStatus(403, 'Forbidden');\n};\n", "export const validateLimitRateParams = (throttle, id) => {\n    if (typeof throttle !== 'number' || throttle < 0) {\n        throw 'The LimitRate throttle has to be a positive number';\n    }\n    if (id && typeof id !== 'string') {\n        throw 'The LimitRate ID has to be a non-empty string';\n    }\n};\n", "import { validateLimitRateParams } from '../validateLimitRateParams/validateLimitRateParams';\nconst getLeftTime = async (id, throttle, storage) => {\n    const lastTime = Number((await storage.get(id)) || 0);\n    return throttle - Date.now() + lastTime;\n};\nexport const isLimitRateHit = async (defaultID, options, storage) => {\n    if (!options.throttle || !storage) {\n        return false;\n    }\n    validateLimitRateParams(options.throttle, options.id);\n    const id = options.id || defaultID;\n    const leftTime = await getLeftTime(id, options.throttle, storage);\n    if (leftTime > 0) {\n        return true;\n    }\n    await storage.set(id, Date.now().toString());\n    return false;\n};\n", "import { EmailJSResponseStatus } from '../../models/EmailJSResponseStatus';\nexport const limitRateError = () => {\n    return new EmailJSResponseStatus(429, 'Too Many Requests');\n};\n", "import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { validateTemplateParams } from '../../utils/validateTemplateParams/validateTemplateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\n/**\n * Send a template to the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {object} templateParams - the template params, what will be set to the EmailJS template\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const send = async (serviceID, templateID, templateParams, options) => {\n    const opts = buildOptions(options);\n    const publicKey = opts.publicKey || store.publicKey;\n    const blockHeadless = opts.blockHeadless || store.blockHeadless;\n    const storageProvider = opts.storageProvider || store.storageProvider;\n    const blockList = { ...store.blockList, ...opts.blockList };\n    const limitRate = { ...store.limitRate, ...opts.limitRate };\n    if (blockHeadless && isHeadless(navigator)) {\n        return Promise.reject(headlessError());\n    }\n    validateParams(publicKey, serviceID, templateID);\n    validateTemplateParams(templateParams);\n    if (templateParams && isBlockedValueInParams(blockList, templateParams)) {\n        return Promise.reject(blockedEmailError());\n    }\n    if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n        return Promise.reject(limitRateError());\n    }\n    const params = {\n        lib_version: '4.4.1',\n        user_id: publicKey,\n        service_id: serviceID,\n        template_id: templateID,\n        template_params: templateParams,\n    };\n    return sendPost('/api/v1.0/email/send', JSON.stringify(params), {\n        'Content-type': 'application/json',\n    });\n};\n", "export const validateForm = (form) => {\n    if (!form || form.nodeName !== 'FORM') {\n        throw 'The 3rd parameter is expected to be the HTML form element or the style selector of the form';\n    }\n};\n", "import { store } from '../../store/store';\nimport { sendPost } from '../../api/sendPost';\nimport { buildOptions } from '../../utils/buildOptions/buildOptions';\nimport { validateForm } from '../../utils/validateForm/validateForm';\nimport { validateParams } from '../../utils/validateParams/validateParams';\nimport { isHeadless } from '../../utils/isHeadless/isHeadless';\nimport { headlessError } from '../../errors/headlessError/headlessError';\nimport { isBlockedValueInParams } from '../../utils/isBlockedValueInParams/isBlockedValueInParams';\nimport { blockedEmailError } from '../../errors/blockedEmailError/blockedEmailError';\nimport { isLimitRateHit } from '../../utils/isLimitRateHit/isLimitRateHit';\nimport { limitRateError } from '../../errors/limitRateError/limitRateError';\nconst findHTMLForm = (form) => {\n    return typeof form === 'string' ? document.querySelector(form) : form;\n};\n/**\n * Send a form the specific EmailJS service\n * @param {string} serviceID - the EmailJS service ID\n * @param {string} templateID - the EmailJS template ID\n * @param {string | HTMLFormElement} form - the form element or selector\n * @param {object} options - the EmailJS SDK config options\n * @returns {Promise<EmailJSResponseStatus>}\n */\nexport const sendForm = async (serviceID, templateID, form, options) => {\n    const opts = buildOptions(options);\n    const publicKey = opts.publicKey || store.publicKey;\n    const blockHeadless = opts.blockHeadless || store.blockHeadless;\n    const storageProvider = store.storageProvider || opts.storageProvider;\n    const blockList = { ...store.blockList, ...opts.blockList };\n    const limitRate = { ...store.limitRate, ...opts.limitRate };\n    if (blockHeadless && isHeadless(navigator)) {\n        return Promise.reject(headlessError());\n    }\n    const currentForm = findHTMLForm(form);\n    validateParams(publicKey, serviceID, templateID);\n    validateForm(currentForm);\n    const formData = new FormData(currentForm);\n    if (isBlockedValueInParams(blockList, formData)) {\n        return Promise.reject(blockedEmailError());\n    }\n    if (await isLimitRateHit(location.pathname, limitRate, storageProvider)) {\n        return Promise.reject(limitRateError());\n    }\n    formData.append('lib_version', '4.4.1');\n    formData.append('service_id', serviceID);\n    formData.append('template_id', templateID);\n    formData.append('user_id', publicKey);\n    return sendPost('/api/v1.0/email/send-form', formData);\n};\n", "import { EmailJSResponseStatus } from './models/EmailJSResponseStatus';\nimport { init } from './methods/init/init';\nimport { send } from './methods/send/send';\nimport { sendForm } from './methods/sendForm/sendForm';\nexport { init, send, sendForm, EmailJSResponseStatus };\nexport default {\n    init,\n    send,\n    sendForm,\n    EmailJSResponseStatus,\n};\n"], "mappings": ";;;AAAO,IAAM,wBAAN,MAA4B;AAAA,EAC/B,YAAY,UAAU,GAAG,QAAQ,iBAAiB;AAC9C,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AACJ;;;ACLO,IAAM,mBAAmB,MAAM;AAClC,MAAI,OAAO,iBAAiB;AACxB;AACJ,SAAO;AAAA,IACH,KAAK,CAAC,QAAQ,QAAQ,QAAQ,aAAa,QAAQ,GAAG,CAAC;AAAA,IACvD,KAAK,CAAC,KAAK,UAAU,QAAQ,QAAQ,aAAa,QAAQ,KAAK,KAAK,CAAC;AAAA,IACrE,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,aAAa,WAAW,GAAG,CAAC;AAAA,EACjE;AACJ;;;ACPO,IAAM,QAAQ;AAAA,EACjB,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,iBAAiB,iBAAiB;AACtC;;;ACLO,IAAM,eAAe,CAAC,YAAY;AACrC,MAAI,CAAC;AACD,WAAO,CAAC;AAEZ,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO;AAAA,MACH,WAAW;AAAA,IACf;AAAA,EACJ;AAEA,MAAI,QAAQ,SAAS,MAAM,mBAAmB;AAC1C,WAAO;AAAA,EACX;AACA,SAAO,CAAC;AACZ;;;ACPO,IAAM,OAAO,CAAC,SAAS,SAAS,8BAA8B;AACjE,MAAI,CAAC;AACD;AACJ,QAAM,OAAO,aAAa,OAAO;AACjC,QAAM,YAAY,KAAK;AACvB,QAAM,gBAAgB,KAAK;AAC3B,QAAM,kBAAkB,KAAK;AAC7B,QAAM,YAAY,KAAK;AACvB,QAAM,YAAY,KAAK;AACvB,QAAM,SAAS,KAAK,UAAU;AAClC;;;ACfO,IAAM,WAAW,OAAO,KAAK,MAAM,UAAU,CAAC,MAAM;AACvD,QAAM,WAAW,MAAM,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7C,QAAQ;AAAA,IACR;AAAA,IACA,MAAM;AAAA,EACV,CAAC;AACD,QAAM,UAAU,MAAM,SAAS,KAAK;AACpC,QAAM,iBAAiB,IAAI,sBAAsB,SAAS,QAAQ,OAAO;AACzE,MAAI,SAAS,IAAI;AACb,WAAO;AAAA,EACX;AACA,QAAM;AACV;;;ACdO,IAAM,iBAAiB,CAAC,WAAW,WAAW,eAAe;AAChE,MAAI,CAAC,aAAa,OAAO,cAAc,UAAU;AAC7C,UAAM;AAAA,EACV;AACA,MAAI,CAAC,aAAa,OAAO,cAAc,UAAU;AAC7C,UAAM;AAAA,EACV;AACA,MAAI,CAAC,cAAc,OAAO,eAAe,UAAU;AAC/C,UAAM;AAAA,EACV;AACJ;;;ACVO,IAAM,yBAAyB,CAAC,mBAAmB;AAEtD,MAAI,kBAAkB,eAAe,SAAS,MAAM,mBAAmB;AACnE,UAAM;AAAA,EACV;AACJ;;;ACLO,IAAM,aAAa,CAACA,eAAc;AACrC,SAAOA,WAAU,aAAa,CAACA,WAAU,aAAaA,WAAU,UAAU,WAAW;AACzF;;;ACDO,IAAM,gBAAgB,MAAM;AAC/B,SAAO,IAAI,sBAAsB,KAAK,kCAAkC;AAC5E;;;ACHO,IAAM,0BAA0B,CAAC,MAAM,kBAAkB;AAC5D,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACtB,UAAM;AAAA,EACV;AACA,MAAI,OAAO,kBAAkB,UAAU;AACnC,UAAM;AAAA,EACV;AACJ;;;ACNA,IAAM,sBAAsB,CAAC,YAAY;AACrC,SAAO,CAAC,QAAQ,MAAM,UAAU,CAAC,QAAQ;AAC7C;AACA,IAAM,WAAW,CAAC,MAAM,SAAS;AAC7B,SAAO,gBAAgB,WAAW,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;AAChE;AACO,IAAM,yBAAyB,CAAC,SAAS,WAAW;AACvD,MAAI,oBAAoB,OAAO;AAC3B,WAAO;AACX,0BAAwB,QAAQ,MAAM,QAAQ,aAAa;AAC3D,QAAM,QAAQ,SAAS,QAAQ,QAAQ,aAAa;AACpD,MAAI,OAAO,UAAU;AACjB,WAAO;AACX,SAAO,QAAQ,KAAK,SAAS,KAAK;AACtC;;;ACdO,IAAM,oBAAoB,MAAM;AACnC,SAAO,IAAI,sBAAsB,KAAK,WAAW;AACrD;;;ACHO,IAAM,0BAA0B,CAAC,UAAU,OAAO;AACrD,MAAI,OAAO,aAAa,YAAY,WAAW,GAAG;AAC9C,UAAM;AAAA,EACV;AACA,MAAI,MAAM,OAAO,OAAO,UAAU;AAC9B,UAAM;AAAA,EACV;AACJ;;;ACNA,IAAM,cAAc,OAAO,IAAI,UAAU,YAAY;AACjD,QAAM,WAAW,OAAQ,MAAM,QAAQ,IAAI,EAAE,KAAM,CAAC;AACpD,SAAO,WAAW,KAAK,IAAI,IAAI;AACnC;AACO,IAAM,iBAAiB,OAAO,WAAW,SAAS,YAAY;AACjE,MAAI,CAAC,QAAQ,YAAY,CAAC,SAAS;AAC/B,WAAO;AAAA,EACX;AACA,0BAAwB,QAAQ,UAAU,QAAQ,EAAE;AACpD,QAAM,KAAK,QAAQ,MAAM;AACzB,QAAM,WAAW,MAAM,YAAY,IAAI,QAAQ,UAAU,OAAO;AAChE,MAAI,WAAW,GAAG;AACd,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,CAAC;AAC3C,SAAO;AACX;;;AChBO,IAAM,iBAAiB,MAAM;AAChC,SAAO,IAAI,sBAAsB,KAAK,mBAAmB;AAC7D;;;ACgBO,IAAM,OAAO,OAAO,WAAW,YAAY,gBAAgB,YAAY;AAC1E,QAAM,OAAO,aAAa,OAAO;AACjC,QAAM,YAAY,KAAK,aAAa,MAAM;AAC1C,QAAM,gBAAgB,KAAK,iBAAiB,MAAM;AAClD,QAAM,kBAAkB,KAAK,mBAAmB,MAAM;AACtD,QAAM,YAAY,EAAE,GAAG,MAAM,WAAW,GAAG,KAAK,UAAU;AAC1D,QAAM,YAAY,EAAE,GAAG,MAAM,WAAW,GAAG,KAAK,UAAU;AAC1D,MAAI,iBAAiB,WAAW,SAAS,GAAG;AACxC,WAAO,QAAQ,OAAO,cAAc,CAAC;AAAA,EACzC;AACA,iBAAe,WAAW,WAAW,UAAU;AAC/C,yBAAuB,cAAc;AACrC,MAAI,kBAAkB,uBAAuB,WAAW,cAAc,GAAG;AACrE,WAAO,QAAQ,OAAO,kBAAkB,CAAC;AAAA,EAC7C;AACA,MAAI,MAAM,eAAe,SAAS,UAAU,WAAW,eAAe,GAAG;AACrE,WAAO,QAAQ,OAAO,eAAe,CAAC;AAAA,EAC1C;AACA,QAAM,SAAS;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EACrB;AACA,SAAO,SAAS,wBAAwB,KAAK,UAAU,MAAM,GAAG;AAAA,IAC5D,gBAAgB;AAAA,EACpB,CAAC;AACL;;;AC/CO,IAAM,eAAe,CAAC,SAAS;AAClC,MAAI,CAAC,QAAQ,KAAK,aAAa,QAAQ;AACnC,UAAM;AAAA,EACV;AACJ;;;ACOA,IAAM,eAAe,CAAC,SAAS;AAC3B,SAAO,OAAO,SAAS,WAAW,SAAS,cAAc,IAAI,IAAI;AACrE;AASO,IAAM,WAAW,OAAO,WAAW,YAAY,MAAM,YAAY;AACpE,QAAM,OAAO,aAAa,OAAO;AACjC,QAAM,YAAY,KAAK,aAAa,MAAM;AAC1C,QAAM,gBAAgB,KAAK,iBAAiB,MAAM;AAClD,QAAM,kBAAkB,MAAM,mBAAmB,KAAK;AACtD,QAAM,YAAY,EAAE,GAAG,MAAM,WAAW,GAAG,KAAK,UAAU;AAC1D,QAAM,YAAY,EAAE,GAAG,MAAM,WAAW,GAAG,KAAK,UAAU;AAC1D,MAAI,iBAAiB,WAAW,SAAS,GAAG;AACxC,WAAO,QAAQ,OAAO,cAAc,CAAC;AAAA,EACzC;AACA,QAAM,cAAc,aAAa,IAAI;AACrC,iBAAe,WAAW,WAAW,UAAU;AAC/C,eAAa,WAAW;AACxB,QAAM,WAAW,IAAI,SAAS,WAAW;AACzC,MAAI,uBAAuB,WAAW,QAAQ,GAAG;AAC7C,WAAO,QAAQ,OAAO,kBAAkB,CAAC;AAAA,EAC7C;AACA,MAAI,MAAM,eAAe,SAAS,UAAU,WAAW,eAAe,GAAG;AACrE,WAAO,QAAQ,OAAO,eAAe,CAAC;AAAA,EAC1C;AACA,WAAS,OAAO,eAAe,OAAO;AACtC,WAAS,OAAO,cAAc,SAAS;AACvC,WAAS,OAAO,eAAe,UAAU;AACzC,WAAS,OAAO,WAAW,SAAS;AACpC,SAAO,SAAS,6BAA6B,QAAQ;AACzD;;;AC1CA,IAAO,aAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": ["navigator"]}