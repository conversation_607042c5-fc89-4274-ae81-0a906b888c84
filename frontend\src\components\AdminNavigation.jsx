import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import {
  LayoutDashboard,
  FileText,
  Settings,
  LogOut,
  Bell,
  Search,
  MessageSquare,
  User,
  Menu,
  X
} from 'lucide-react'

const AdminNavigation = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout } = useAuth()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [notifications] = useState([
    { id: 1, message: 'New job application received', time: '2 min ago', unread: true },
    { id: 2, message: 'Quote request from <PERSON>', time: '5 min ago', unread: true },
    { id: 3, message: 'Internship application submitted', time: '1 hour ago', unread: false }
  ])
  const [showNotifications, setShowNotifications] = useState(false)

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to logout?')) {
      logout()
      navigate('/admin/login', { replace: true })
    }
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(prev => !prev)
  }

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false)
  }

  const navItems = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
      description: 'Overview and analytics'
    },
    {
      name: 'Applications',
      href: '/admin/applications',
      icon: FileText,
      description: 'Review applications'
    },
    {
      name: 'Quotes',
      href: '/admin/quotes',
      icon: MessageSquare,
      description: 'Manage quote requests'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: Settings,
      description: 'Manage system users'
    }
  ]

  const isActive = (href) => {
    return location.pathname === href || (href === '/admin/dashboard' && location.pathname === '/admin')
  }

  const unreadCount = notifications.filter(n => n.unread).length

  return (
    <div>
      <header className="fixed top-0 left-0 right-0 z-50 bg-dark-950/95 backdrop-blur-xl border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link to="/admin/dashboard" className="flex items-center space-x-3" onClick={closeMobileMenu}>
              <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">DX</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-white font-bold text-lg">Admin Panel</span>
                <div className="text-xs text-gray-400">Delta Xero Creations</div>
              </div>
            </Link>

            <nav className="hidden lg:flex items-center space-x-1">
              {navItems.map(({ name, href, icon: Icon, description }) => (
                <Link key={name} to={href} className="relative group">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                      isActive(href)
                        ? 'bg-primary-500 text-white shadow-lg shadow-primary-500/25'
                        : 'text-gray-300 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Icon size={18} />
                    <span className="font-medium">{name}</span>
                  </motion.div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-dark-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
                    {description}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-dark-800"></div>
                  </div>
                </Link>
              ))}
            </nav>

            <div className="flex items-center space-x-2 md:space-x-4">
              <button className="hidden md:block p-2 text-gray-400 hover:text-white transition-colors duration-300">
                <Search size={20} />
              </button>

              <div className="relative">
                <button
                  onClick={() => setShowNotifications(prev => !prev)}
                  className="relative p-2 text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <Bell size={20} />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </button>

                <AnimatePresence>
                  {showNotifications && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      className="absolute right-0 top-full mt-2 w-80 bg-dark-800 border border-gray-700 rounded-xl shadow-xl z-50"
                    >
                      <div className="p-4 border-b border-gray-700">
                        <h3 className="text-white font-semibold">Notifications</h3>
                        <p className="text-gray-400 text-sm">{unreadCount} unread</p>
                      </div>
                      <div className="max-h-64 overflow-y-auto">
                        {notifications.length > 0 ? notifications.map((notification) => (
                          <Link
                            key={notification.id}
                            to={notification.link || '#'}
                            onClick={() => {
                              markAsRead(notification.id);
                              setShowNotifications(false);
                            }}
                            className={`block p-4 border-b border-gray-700/50 hover:bg-gray-700/30 transition-colors ${
                              notification.unread ? 'bg-primary-500/5' : ''
                            }`}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${notification.unread ? 'bg-primary-500' : 'bg-gray-600'}`}></div>
                              <div className="flex-1 min-w-0">
                                <p className="text-white text-sm font-medium">{notification.title || 'Notification'}</p>
                                <p className="text-gray-300 text-sm mt-1 truncate">{notification.message}</p>
                                <p className="text-gray-400 text-xs mt-1">{notification.time}</p>
                              </div>
                            </div>
                          </Link>
                        )) : (
                          <div className="p-4 text-center">
                            <p className="text-gray-400 text-sm">No notifications</p>
                          </div>
                        )}
                      </div>
                      <div className="p-3 border-t border-gray-700 flex justify-between items-center">
                        <button
                          onClick={markAllAsRead}
                          className="text-gray-400 text-sm hover:text-gray-300 transition-colors"
                        >
                          Mark all read
                        </button>
                        <button className="text-primary-400 text-sm hover:text-primary-300 transition-colors">
                          View all →
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <div className="flex items-center space-x-2 md:space-x-3 px-2 md:px-3 py-2 bg-white/5 rounded-xl">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <User size={16} className="text-white" />
                </div>
                <div className="hidden md:block">
                  <p className="text-white text-sm font-medium">{user?.username || 'Admin'}</p>
                  <p className="text-gray-400 text-xs capitalize">{user?.role || 'admin'}</p>
                </div>
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-2 md:px-4 py-2 bg-red-500/20 text-red-400 rounded-xl hover:bg-red-500/30 transition-all duration-300"
              >
                <LogOut size={16} />
                <span className="hidden md:inline">Logout</span>
              </button>

              <button
                onClick={toggleMobileMenu}
                className="lg:hidden p-2 text-gray-400 hover:text-white transition-colors duration-300"
              >
                {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>
      </header>

      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 lg:hidden"
            onClick={closeMobileMenu}
          >
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="fixed top-16 right-0 bottom-0 w-80 bg-dark-950/95 backdrop-blur-xl border-l border-white/10 z-50 lg:hidden"
          >
            <div className="p-6">
              <div className="space-y-4">
                {navItems.map(({ name, href, icon: Icon, description }) => (
                  <Link
                    key={name}
                    to={href}
                    onClick={closeMobileMenu}
                    className={`flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 ${
                      isActive(href)
                        ? 'bg-primary-500 text-white shadow-lg shadow-primary-500/25'
                        : 'text-gray-300 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Icon size={20} />
                    <div>
                      <div className="font-medium">{name}</div>
                      <div className="text-xs opacity-70">{description}</div>
                    </div>
                  </Link>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-700">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <User size={18} className="text-white" />
                  </div>
                  <div>
                    <p className="text-white font-medium">{user?.username || 'Admin'}</p>
                    <p className="text-gray-400 text-sm capitalize">{user?.role || 'admin'}</p>
                  </div>
                </div>

                <button
                  onClick={() => {
                    handleLogout()
                    closeMobileMenu()
                  }}
                  className="w-full flex items-center justify-center space-x-2 p-3 bg-red-500/20 text-red-400 rounded-xl hover:bg-red-500/30 transition-all duration-300"
                >
                  <LogOut size={16} />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default AdminNavigation
